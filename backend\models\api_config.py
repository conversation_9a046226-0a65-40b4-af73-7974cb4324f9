"""
AI模型API配置数据模型
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, JSON, DateTime, Enum, Boolean, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field
from enum import Enum as PyEnum
import json

Base = declarative_base()

class APIStatus(PyEnum):
    """API状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive" 
    ERROR = "error"
    TESTING = "testing"

class APIPlatform(PyEnum):
    """AI平台枚举"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    CLAUDE = "claude"
    CUSTOM = "custom"

class APIConfig(Base):
    """API配置数据模型"""
    __tablename__ = "api_configs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    platform = Column(Enum(APIPlatform), nullable=False)
    model_name = Column(String(100), nullable=False)
    display_name = Column(String(200))
    api_key_encrypted = Column(Text, nullable=False)
    endpoint_url = Column(String(500))
    organization_id = Column(String(100))
    project_id = Column(String(100))
    
    # 模型参数配置
    parameters = Column(JSON, default={})
    
    # 调用限制配置
    rate_limits = Column(JSON, default={})
    
    # 成本控制配置
    cost_limits = Column(JSON, default={})
    
    # 安全配置
    security_config = Column(JSON, default={})
    
    # 负载均衡权重
    weight = Column(Integer, default=100)
    
    # 状态和监控
    status = Column(Enum(APIStatus), default=APIStatus.INACTIVE)
    last_test_time = Column(DateTime)
    last_error_message = Column(Text)
    
    # 统计信息
    total_calls = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    total_cost = Column(DECIMAL(10, 4), default=0)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class APICallLog(Base):
    """API调用日志数据模型"""
    __tablename__ = "api_call_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    api_config_id = Column(Integer, nullable=False)
    
    # 请求信息
    request_data = Column(JSON)
    business_scene = Column(String(50))  # 业务场景
    
    # 响应信息
    response_data = Column(JSON)
    status_code = Column(Integer)
    response_time_ms = Column(Integer)
    
    # Token和成本
    prompt_tokens = Column(Integer, default=0)
    completion_tokens = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    cost_amount = Column(DECIMAL(10, 4), default=0)
    
    # 错误信息
    error_message = Column(Text)
    error_type = Column(String(50))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)

# Pydantic模型用于API接口

class ModelParameters(BaseModel):
    """模型参数配置"""
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2048, ge=1, le=8192)
    top_p: float = Field(default=1.0, ge=0.0, le=1.0)
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    stop_sequences: List[str] = Field(default=[])
    seed: Optional[int] = None
    response_format: str = Field(default="text")

class RateLimits(BaseModel):
    """调用频率限制配置"""
    requests_per_minute: int = Field(default=60, ge=1)
    requests_per_hour: int = Field(default=1000, ge=1)
    requests_per_day: int = Field(default=10000, ge=1)
    tokens_per_minute: int = Field(default=40000, ge=1)
    concurrent_requests: int = Field(default=10, ge=1)

class CostLimits(BaseModel):
    """成本控制配置"""
    daily_budget: float = Field(default=100.0, ge=0)
    monthly_budget: float = Field(default=1000.0, ge=0)
    cost_per_1k_tokens: float = Field(default=0.002, ge=0)
    warning_threshold: float = Field(default=0.8, ge=0, le=1.0)
    stop_threshold: float = Field(default=0.95, ge=0, le=1.0)

class SecurityConfig(BaseModel):
    """安全配置"""
    ip_whitelist: List[str] = Field(default=[])
    time_restrictions: Dict[str, str] = Field(default={})  # {"start": "09:00", "end": "18:00"}
    key_rotation_days: int = Field(default=30, ge=1)
    enable_audit_log: bool = Field(default=True)

class APIConfigCreate(BaseModel):
    """创建API配置请求模型"""
    platform: APIPlatform
    model_name: str = Field(..., min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, max_length=200)
    api_key: str = Field(..., min_length=1)
    endpoint_url: Optional[str] = None
    organization_id: Optional[str] = None
    project_id: Optional[str] = None
    parameters: ModelParameters = Field(default_factory=ModelParameters)
    rate_limits: RateLimits = Field(default_factory=RateLimits)
    cost_limits: CostLimits = Field(default_factory=CostLimits)
    security_config: SecurityConfig = Field(default_factory=SecurityConfig)
    weight: int = Field(default=100, ge=0, le=1000)

class APIConfigUpdate(BaseModel):
    """更新API配置请求模型"""
    display_name: Optional[str] = None
    api_key: Optional[str] = None
    endpoint_url: Optional[str] = None
    organization_id: Optional[str] = None
    project_id: Optional[str] = None
    parameters: Optional[ModelParameters] = None
    rate_limits: Optional[RateLimits] = None
    cost_limits: Optional[CostLimits] = None
    security_config: Optional[SecurityConfig] = None
    weight: Optional[int] = Field(None, ge=0, le=1000)
    status: Optional[APIStatus] = None

class APIConfigResponse(BaseModel):
    """API配置响应模型"""
    id: int
    platform: APIPlatform
    model_name: str
    display_name: Optional[str]
    endpoint_url: Optional[str]
    parameters: ModelParameters
    rate_limits: RateLimits
    cost_limits: CostLimits
    security_config: SecurityConfig
    weight: int
    status: APIStatus
    last_test_time: Optional[datetime]
    last_error_message: Optional[str]
    total_calls: int
    total_tokens: int
    total_cost: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class APICallLogResponse(BaseModel):
    """API调用日志响应模型"""
    id: int
    user_id: int
    api_config_id: int
    business_scene: Optional[str]
    status_code: Optional[int]
    response_time_ms: Optional[int]
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_amount: float
    error_message: Optional[str]
    error_type: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

class APITestRequest(BaseModel):
    """API测试请求模型"""
    prompt: str = Field(default="Hello, this is a test message.")
    parameters: Optional[ModelParameters] = None

class APITestResponse(BaseModel):
    """API测试响应模型"""
    success: bool
    response_text: Optional[str]
    response_time_ms: int
    tokens_used: int
    cost_estimate: float
    error_message: Optional[str]

class LoadBalanceConfig(BaseModel):
    """负载均衡配置"""
    strategy: str = Field(default="weighted_round_robin")  # weighted_round_robin, cost_optimized, performance_optimized
    fallback_enabled: bool = Field(default=True)
    timeout_seconds: int = Field(default=30, ge=1, le=300)
    retry_attempts: int = Field(default=3, ge=1, le=10)
    health_check_interval: int = Field(default=60, ge=10)  # seconds

class BusinessSceneRouting(BaseModel):
    """业务场景路由配置"""
    scene_name: str
    primary_model: str
    fallback_models: List[str] = Field(default=[])
    custom_parameters: Optional[ModelParameters] = None
    priority: int = Field(default=100, ge=1, le=1000)
