# AI智能销售CPQ系统 - 前端

基于Vue3 + Element Plus构建的AI驱动智能销售CPQ系统前端应用。

## 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **图表库**: ECharts 5.4+
- **日期处理**: Day.js 1.11+
- **工具库**: Lodash-es 4.17+

## 功能特性

### 核心功能
- 🤖 **AI智能对话**: 多轮对话需求收集和方案生成
- 📋 **需求管理**: 智能需求收集、分析和整理
- 📄 **方案生成**: AI驱动的解决方案自动生成
- 💰 **智能报价**: 自动化报价引擎和预算优化
- 📑 **合同管理**: 合同生成、审核和签署流程
- 📚 **知识库**: 产品库、案例库和规则管理

### 系统功能
- 🔐 **用户认证**: JWT认证和角色权限管理
- 🎨 **主题切换**: 明暗主题和自定义主色调
- 📱 **响应式设计**: 支持PC和移动端
- 🌐 **国际化**: 多语言支持
- 📊 **数据可视化**: 丰富的图表和统计面板

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   ├── components/        # 公共组件
│   ├── layout/           # 布局组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── styles/           # 全局样式
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── .env                  # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── index.html            # HTML模板
├── package.json          # 依赖配置
├── vite.config.js        # Vite配置
└── README.md             # 项目说明
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发运行
```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:3000

### 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 代码规范
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 核心组件说明

### AIChat组件
智能对话组件，支持：
- 多轮对话管理
- 消息类型处理（文本、数据、错误等）
- 实时输入状态
- 消息操作（复制、重新生成、评分）
- 全屏模式
- 设置配置

### 路由结构
- `/login` - 登录页面
- `/dashboard` - 工作台
- `/requirements` - 需求管理
  - `/collection` - 需求收集
  - `/analysis` - 需求分析
- `/solutions` - 方案管理
  - `/generation` - 方案生成
  - `/editing` - 方案编辑
  - `/templates` - 方案模板
- `/pricing` - 报价管理
- `/contracts` - 合同管理
- `/knowledge` - 知识库
- `/admin` - 系统管理

### 状态管理
使用Pinia进行状态管理：
- `user` - 用户信息和认证状态
- `theme` - 主题配置和UI状态
- `chat` - 对话状态管理
- `requirement` - 需求数据管理
- `solution` - 方案数据管理

## API集成

### 请求拦截器
- 自动添加认证token
- 请求ID追踪
- 加载状态管理

### 响应拦截器
- 统一错误处理
- 认证失效处理
- 响应数据格式化

### 错误处理
- 网络错误提示
- 业务错误处理
- 用户友好的错误信息

## 样式规范

### CSS变量
使用CSS变量支持主题切换：
```css
:root {
  --el-color-primary: #409EFF;
  --el-text-color-primary: #303133;
  /* ... */
}
```

### 响应式断点
- `768px` - 移动端
- `992px` - 平板端
- `1200px` - 桌面端

### 工具类
提供丰富的工具类：
- 布局：`flex`, `grid`, `gap-*`
- 间距：`p-*`, `m-*`
- 文本：`text-*`, `font-*`
- 颜色：`text-primary`, `bg-success`

## 部署说明

### 环境变量配置
根据部署环境配置相应的环境变量文件。

### 构建优化
- 代码分割和懒加载
- 静态资源压缩
- CDN资源优化
- PWA支持

### 服务器配置
需要配置：
- 静态文件服务
- 路由回退到index.html
- API代理配置
- HTTPS证书

## 开发规范

### 组件开发
- 使用Composition API
- 组件名使用PascalCase
- Props定义类型和默认值
- 事件使用kebab-case

### 代码风格
- 使用ESLint和Prettier
- 变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 文件名使用kebab-case

### Git提交
使用约定式提交格式：
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建配置更新
```

## 性能优化

### 代码分割
- 路由级别的代码分割
- 组件懒加载
- 第三方库分离

### 资源优化
- 图片压缩和WebP格式
- 字体文件优化
- CSS和JS压缩

### 运行时优化
- 虚拟滚动
- 防抖和节流
- 缓存策略

## 常见问题

### 开发环境问题
1. **端口冲突**: 修改vite.config.js中的端口配置
2. **代理失败**: 检查后端服务是否启动
3. **热更新失败**: 清除node_modules重新安装

### 构建问题
1. **内存不足**: 增加Node.js内存限制
2. **路径错误**: 检查base配置
3. **依赖冲突**: 使用npm ls检查依赖树

## 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 文档：https://docs.ai-cpq.com
- 问题反馈：https://github.com/ai-cpq/frontend/issues
