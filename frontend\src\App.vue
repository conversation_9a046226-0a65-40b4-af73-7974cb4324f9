<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'

const userStore = useUserStore()
const themeStore = useThemeStore()

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  
  // 检查用户登录状态
  userStore.checkAuth()
})
</script>

<style>
#app {
  height: 100vh;
  width: 100vw;
}
</style>
