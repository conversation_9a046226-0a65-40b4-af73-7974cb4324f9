import { http } from './request'

export const authAPI = {
  // 用户登录
  login(data) {
    return http.post('/auth/login', data)
  },

  // 用户登出
  logout() {
    return http.post('/auth/logout')
  },

  // 获取用户信息
  getUserInfo() {
    return http.get('/auth/user')
  },

  // 刷新token
  refreshToken() {
    return http.post('/auth/refresh')
  },

  // 修改密码
  changePassword(data) {
    return http.post('/auth/change-password', data)
  },

  // 更新用户资料
  updateProfile(data) {
    return http.put('/auth/profile', data)
  },

  // 忘记密码
  forgotPassword(data) {
    return http.post('/auth/forgot-password', data)
  },

  // 重置密码
  resetPassword(data) {
    return http.post('/auth/reset-password', data)
  },

  // 验证邮箱
  verifyEmail(data) {
    return http.post('/auth/verify-email', data)
  },

  // 发送验证码
  sendVerificationCode(data) {
    return http.post('/auth/send-verification-code', data)
  }
}
