import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    
    // 添加认证token
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()
    
    // 显示加载状态（可选）
    if (config.showLoading !== false) {
      // 可以在这里添加全局loading
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data, config } = response
    
    // 隐藏加载状态
    if (config.showLoading !== false) {
      // 隐藏loading
    }
    
    // 统一处理响应数据
    if (data.code !== undefined) {
      if (data.code === 200 || data.code === 0) {
        return data
      } else {
        // 业务错误
        const errorMessage = data.message || '请求失败'
        if (config.showError !== false) {
          ElMessage.error(errorMessage)
        }
        return Promise.reject(new Error(errorMessage))
      }
    }
    
    // 直接返回数据
    return data
  },
  (error) => {
    const { response, config } = error
    
    // 隐藏加载状态
    if (config?.showLoading !== false) {
      // 隐藏loading
    }
    
    let errorMessage = '网络错误，请稍后重试'
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 400:
          errorMessage = data?.message || '请求参数错误'
          break
        case 401:
          errorMessage = '登录已过期，请重新登录'
          handleUnauthorized()
          break
        case 403:
          errorMessage = '没有访问权限'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 422:
          errorMessage = data?.message || '数据验证失败'
          break
        case 429:
          errorMessage = '请求过于频繁，请稍后重试'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
          errorMessage = '网关错误'
          break
        case 503:
          errorMessage = '服务暂时不可用'
          break
        default:
          errorMessage = data?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请稍后重试'
    } else if (error.message === 'Network Error') {
      errorMessage = '网络连接失败，请检查网络'
    }
    
    // 显示错误信息
    if (config?.showError !== false) {
      ElMessage.error(errorMessage)
    }
    
    return Promise.reject(error)
  }
)

// 处理未授权
const handleUnauthorized = () => {
  const userStore = useUserStore()
  
  ElMessageBox.confirm(
    '登录状态已过期，您可以继续留在该页面，或者重新登录',
    '系统提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout().then(() => {
      router.push('/login')
    })
  })
}

// 生成请求ID
const generateRequestId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 请求方法封装
export const http = {
  get(url, params = {}, config = {}) {
    return request({
      method: 'GET',
      url,
      params,
      ...config
    })
  },
  
  post(url, data = {}, config = {}) {
    return request({
      method: 'POST',
      url,
      data,
      ...config
    })
  },
  
  put(url, data = {}, config = {}) {
    return request({
      method: 'PUT',
      url,
      data,
      ...config
    })
  },
  
  patch(url, data = {}, config = {}) {
    return request({
      method: 'PATCH',
      url,
      data,
      ...config
    })
  },
  
  delete(url, config = {}) {
    return request({
      method: 'DELETE',
      url,
      ...config
    })
  },
  
  upload(url, formData, config = {}) {
    return request({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },
  
  download(url, params = {}, config = {}) {
    return request({
      method: 'GET',
      url,
      params,
      responseType: 'blob',
      ...config
    })
  }
}

export default request
