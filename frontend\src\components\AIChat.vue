<template>
  <div class="ai-chat" :class="{ 'chat-fullscreen': isFullscreen }">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="chat-info">
        <el-avatar :size="32">
          <el-icon><Robot /></el-icon>
        </el-avatar>
        <div class="chat-details">
          <div class="chat-title">{{ chatTitle }}</div>
          <div class="chat-status">
            <el-tag :type="statusType" size="small">{{ statusText }}</el-tag>
            <span class="message-count">{{ messages.length }} 条消息</span>
          </div>
        </div>
      </div>
      
      <div class="chat-actions">
        <el-tooltip content="清空对话">
          <el-button text @click="clearChat">
            <el-icon><Delete /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="导出对话">
          <el-button text @click="exportChat">
            <el-icon><Download /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏显示'">
          <el-button text @click="toggleFullscreen">
            <el-icon>
              <component :is="isFullscreen ? 'OffScreen' : 'FullScreen'" />
            </el-icon>
          </el-button>
        </el-tooltip>
        
        <el-dropdown @command="handleMenuCommand">
          <el-button text>
            <el-icon><More /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                聊天设置
              </el-dropdown-item>
              <el-dropdown-item command="history">
                <el-icon><Clock /></el-icon>
                历史记录
              </el-dropdown-item>
              <el-dropdown-item command="feedback">
                <el-icon><ChatDotRound /></el-icon>
                反馈建议
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="chat-messages" ref="messagesContainer">
      <div class="messages-wrapper">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-avatar">
            <el-icon><Robot /></el-icon>
          </div>
          <div class="welcome-content">
            <h3>{{ welcomeTitle }}</h3>
            <p>{{ welcomeDescription }}</p>
            <div class="quick-actions">
              <el-button
                v-for="action in quickActions"
                :key="action.text"
                size="small"
                @click="sendQuickMessage(action.text)"
              >
                {{ action.text }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div
          v-for="(message, index) in messages"
          :key="message.id"
          class="message-item"
          :class="{
            'message-user': message.role === 'user',
            'message-assistant': message.role === 'assistant',
            'message-system': message.role === 'system'
          }"
        >
          <div class="message-avatar">
            <el-avatar :size="32">
              <el-icon v-if="message.role === 'assistant'">
                <Robot />
              </el-icon>
              <el-icon v-else-if="message.role === 'system'">
                <InfoFilled />
              </el-icon>
              <span v-else>{{ getUserInitial() }}</span>
            </el-avatar>
          </div>

          <div class="message-content">
            <div class="message-header">
              <span class="message-sender">{{ getSenderName(message.role) }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>

            <div class="message-body">
              <!-- 文本消息 -->
              <div
                v-if="message.type === 'text'"
                class="message-text"
                v-html="formatMessageContent(message.content)"
              ></div>

              <!-- 加载状态 -->
              <div v-if="message.type === 'loading'" class="message-loading">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span class="loading-text">{{ message.content }}</span>
              </div>

              <!-- 错误消息 -->
              <div v-if="message.type === 'error'" class="message-error">
                <el-icon><WarningFilled /></el-icon>
                <span>{{ message.content }}</span>
                <el-button text size="small" @click="retryMessage(index)">
                  重试
                </el-button>
              </div>

              <!-- 结构化数据 -->
              <div v-if="message.type === 'data'" class="message-data">
                <div class="data-title">{{ message.title }}</div>
                <el-table :data="message.data" size="small" style="width: 100%">
                  <el-table-column
                    v-for="column in message.columns"
                    :key="column.prop"
                    :prop="column.prop"
                    :label="column.label"
                    :width="column.width"
                  />
                </el-table>
              </div>
            </div>

            <!-- 消息操作 -->
            <div v-if="message.role === 'assistant' && message.type === 'text'" class="message-actions">
              <el-button text size="small" @click="copyMessage(message.content)">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              
              <el-button text size="small" @click="regenerateMessage(index)">
                <el-icon><Refresh /></el-icon>
                重新生成
              </el-button>
              
              <el-button text size="small" @click="rateMessage(message, 'like')">
                <el-icon><Like /></el-icon>
                {{ message.rating === 'like' ? '已赞' : '赞' }}
              </el-button>
              
              <el-button text size="small" @click="rateMessage(message, 'dislike')">
                <el-icon><Dislike /></el-icon>
                {{ message.rating === 'dislike' ? '已踩' : '踩' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="typing-message">
          <div class="message-avatar">
            <el-avatar :size="32">
              <el-icon><Robot /></el-icon>
            </el-avatar>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <!-- 建议回复 -->
      <div v-if="suggestions.length > 0" class="suggestions">
        <div class="suggestions-title">建议回复：</div>
        <div class="suggestion-chips">
          <el-tag
            v-for="suggestion in suggestions"
            :key="suggestion"
            class="suggestion-chip"
            @click="sendSuggestion(suggestion)"
          >
            {{ suggestion }}
          </el-tag>
        </div>
      </div>

      <!-- 输入框 -->
      <div class="input-container">
        <div class="input-toolbar">
          <el-button text size="small" @click="showEmojiPicker = !showEmojiPicker">
            <el-icon><Smile /></el-icon>
          </el-button>
          
          <el-upload
            :show-file-list="false"
            :before-upload="handleFileUpload"
            accept="image/*,.pdf,.doc,.docx,.txt"
          >
            <el-button text size="small">
              <el-icon><Paperclip /></el-icon>
            </el-button>
          </el-upload>
          
          <el-button text size="small" @click="toggleVoiceInput">
            <el-icon><Microphone /></el-icon>
          </el-button>
        </div>

        <div class="input-wrapper">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="inputRows"
            :placeholder="inputPlaceholder"
            :disabled="isTyping"
            @keydown="handleKeyDown"
            @input="handleInput"
            resize="none"
            class="message-input"
          />
          
          <div class="input-actions">
            <div class="input-info">
              <span class="char-count">{{ inputMessage.length }}/{{ maxLength }}</span>
              <span class="input-hint">{{ inputHint }}</span>
            </div>
            
            <el-button
              type="primary"
              :loading="isTyping"
              :disabled="!canSend"
              @click="sendMessage"
              class="send-button"
            >
              <el-icon><Promotion /></el-icon>
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="聊天设置" width="500px">
      <el-form :model="chatSettings" label-width="120px">
        <el-form-item label="AI模型">
          <el-select v-model="chatSettings.model" style="width: 100%">
            <el-option label="GPT-4 (推荐)" value="gpt-4" />
            <el-option label="Claude-3" value="claude-3" />
            <el-option label="DeepSeek" value="deepseek" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="回复风格">
          <el-radio-group v-model="chatSettings.style">
            <el-radio label="professional">专业</el-radio>
            <el-radio label="friendly">友好</el-radio>
            <el-radio label="concise">简洁</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="自动建议">
          <el-switch v-model="chatSettings.autoSuggestions" />
        </el-form-item>
        
        <el-form-item label="声音提醒">
          <el-switch v-model="chatSettings.soundNotification" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Robot,
  Delete,
  Download,
  FullScreen,
  OffScreen,
  More,
  Setting,
  Clock,
  ChatDotRound,
  InfoFilled,
  WarningFilled,
  CopyDocument,
  Refresh,
  Like,
  Dislike,
  Smile,
  Paperclip,
  Microphone,
  Promotion
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  chatTitle: {
    type: String,
    default: 'AI助手'
  },
  welcomeTitle: {
    type: String,
    default: '您好！我是AI助手'
  },
  welcomeDescription: {
    type: String,
    default: '我可以帮助您解答问题、分析需求、生成方案等。请告诉我您需要什么帮助？'
  },
  quickActions: {
    type: Array,
    default: () => [
      { text: '帮我分析需求' },
      { text: '生成解决方案' },
      { text: '计算报价' },
      { text: '查看案例' }
    ]
  },
  maxLength: {
    type: Number,
    default: 2000
  }
})

// Emits
const emit = defineEmits(['message-sent', 'chat-cleared', 'settings-changed'])

const userStore = useUserStore()

// 响应式数据
const messagesContainer = ref()
const inputMessage = ref('')
const isTyping = ref(false)
const isFullscreen = ref(false)
const showSettings = ref(false)
const showEmojiPicker = ref(false)
const inputRows = ref(2)

// 消息列表
const messages = ref([])

// 建议回复
const suggestions = ref([])

// 聊天设置
const chatSettings = reactive({
  model: 'gpt-4',
  style: 'professional',
  autoSuggestions: true,
  soundNotification: true
})

// 计算属性
const statusType = computed(() => {
  if (isTyping.value) return 'warning'
  if (messages.value.length === 0) return 'info'
  return 'success'
})

const statusText = computed(() => {
  if (isTyping.value) return '正在输入...'
  if (messages.value.length === 0) return '等待开始'
  return '在线'
})

const canSend = computed(() => {
  return inputMessage.value.trim().length > 0 && 
         inputMessage.value.length <= props.maxLength &&
         !isTyping.value
})

const inputPlaceholder = computed(() => {
  if (isTyping.value) return 'AI正在思考中，请稍候...'
  return '输入您的问题或需求...'
})

const inputHint = computed(() => {
  if (inputMessage.value.length > props.maxLength) {
    return '内容过长'
  }
  return 'Ctrl+Enter 发送，Shift+Enter 换行'
})

// 方法
const getUserInitial = () => {
  return userStore.userName.charAt(0).toUpperCase()
}

const getSenderName = (role) => {
  const names = {
    user: userStore.userName,
    assistant: 'AI助手',
    system: '系统'
  }
  return names[role] || role
}

const formatTime = (timestamp) => {
  return dayjs(timestamp).format('HH:mm')
}

const formatMessageContent = (content) => {
  // 简单的markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

const handleKeyDown = (event) => {
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    sendMessage()
  } else if (event.shiftKey && event.key === 'Enter') {
    // 允许换行
    return
  } else if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const handleInput = () => {
  // 自动调整输入框高度
  const lines = inputMessage.value.split('\n').length
  inputRows.value = Math.min(Math.max(lines, 2), 6)
}

const sendMessage = async () => {
  if (!canSend.value) return

  const userMessage = {
    id: Date.now(),
    role: 'user',
    type: 'text',
    content: inputMessage.value.trim(),
    timestamp: new Date().toISOString()
  }

  messages.value.push(userMessage)
  const userInput = inputMessage.value.trim()
  inputMessage.value = ''
  inputRows.value = 2
  suggestions.value = []

  emit('message-sent', userMessage)

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始AI响应
  isTyping.value = true

  try {
    // 模拟AI响应
    const response = await generateAIResponse(userInput)
    
    const aiMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      type: 'text',
      content: response.content,
      timestamp: new Date().toISOString()
    }

    messages.value.push(aiMessage)

    // 生成建议回复
    if (chatSettings.autoSuggestions) {
      suggestions.value = response.suggestions || []
    }

    // 声音提醒
    if (chatSettings.soundNotification) {
      playNotificationSound()
    }

  } catch (error) {
    const errorMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      type: 'error',
      content: '抱歉，我遇到了一些问题，请稍后重试。',
      timestamp: new Date().toISOString()
    }
    messages.value.push(errorMessage)
  } finally {
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

const sendQuickMessage = (text) => {
  inputMessage.value = text
  sendMessage()
}

const sendSuggestion = (suggestion) => {
  inputMessage.value = suggestion
  suggestions.value = []
  sendMessage()
}

const generateAIResponse = async (userInput) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000))

  // 模拟不同类型的响应
  const responses = [
    {
      content: '我理解您的需求。让我为您分析一下关键要点：\n\n**主要目标**：提升业务效率\n**技术要求**：云端部署\n**预算考虑**：成本控制\n\n您希望我重点关注哪个方面？',
      suggestions: ['技术实现方案', '成本预算分析', '实施时间规划']
    },
    {
      content: '根据您提供的信息，我建议采用以下解决方案：\n\n1. **模块化架构设计**\n2. **微服务技术栈**\n3. **容器化部署**\n\n这样可以确保系统的可扩展性和维护性。需要我详细说明某个方面吗？',
      suggestions: ['详细技术方案', '实施步骤', '风险评估']
    },
    {
      content: '好的，我已经记录了您的需求。基于类似项目的经验，预估的项目周期为8-12周，预算范围在50-80万元。\n\n让我们进一步讨论具体的功能模块吧。',
      suggestions: ['功能模块详情', '时间安排', '团队配置']
    }
  ]

  return responses[Math.floor(Math.random() * responses.length)]
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content.replace(/<[^>]*>/g, ''))
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const regenerateMessage = async (index) => {
  const message = messages.value[index]
  if (message.role !== 'assistant') return

  // 找到对应的用户消息
  const userMessage = messages.value[index - 1]
  if (!userMessage || userMessage.role !== 'user') return

  isTyping.value = true

  try {
    const response = await generateAIResponse(userMessage.content)
    message.content = response.content
    message.timestamp = new Date().toISOString()
    
    ElMessage.success('已重新生成回复')
  } catch (error) {
    ElMessage.error('重新生成失败')
  } finally {
    isTyping.value = false
  }
}

const rateMessage = (message, rating) => {
  message.rating = message.rating === rating ? null : rating
  ElMessage.success(rating === 'like' ? '感谢您的反馈' : '我们会改进的')
}

const retryMessage = (index) => {
  const message = messages.value[index]
  if (message.type === 'error') {
    regenerateMessage(index)
  }
}

const clearChat = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有对话记录吗？', '确认操作', {
      type: 'warning'
    })
    
    messages.value = []
    suggestions.value = []
    emit('chat-cleared')
    ElMessage.success('对话已清空')
  } catch (error) {
    // 用户取消
  }
}

const exportChat = () => {
  const chatContent = messages.value.map(msg => {
    const time = formatTime(msg.timestamp)
    const sender = getSenderName(msg.role)
    const content = msg.content.replace(/<[^>]*>/g, '')
    return `[${time}] ${sender}: ${content}`
  }).join('\n\n')

  const blob = new Blob([chatContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${dayjs().format('YYYY-MM-DD-HH-mm')}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('对话已导出')
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const handleMenuCommand = (command) => {
  switch (command) {
    case 'settings':
      showSettings.value = true
      break
    case 'history':
      ElMessage.info('历史记录功能开发中')
      break
    case 'feedback':
      ElMessage.info('反馈功能开发中')
      break
  }
}

const handleFileUpload = (file) => {
  ElMessage.info('文件上传功能开发中')
  return false
}

const toggleVoiceInput = () => {
  ElMessage.info('语音输入功能开发中')
}

const saveSettings = () => {
  showSettings.value = false
  emit('settings-changed', chatSettings)
  ElMessage.success('设置已保存')
}

const playNotificationSound = () => {
  // 播放提示音
  try {
    const audio = new Audio('/notification.mp3')
    audio.volume = 0.3
    audio.play().catch(() => {
      // 忽略播放失败
    })
  } catch (error) {
    // 忽略错误
  }
}

// 生命周期
onMounted(() => {
  // 监听键盘快捷键
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isFullscreen.value) {
      isFullscreen.value = false
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('keydown', () => {})
})
</script>

<style scoped>
.ai-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.chat-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  border-radius: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.chat-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.message-count {
  color: var(--el-text-color-placeholder);
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.messages-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

.welcome-avatar {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.welcome-content h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.welcome-content p {
  margin: 0 0 20px 0;
  color: var(--el-text-color-secondary);
  line-height: 1.5;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.message-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.message-user {
  flex-direction: row-reverse;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 44px);
}

.message-user .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.message-user .message-header {
  flex-direction: row-reverse;
}

.message-sender {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.message-time {
  color: var(--el-text-color-placeholder);
}

.message-body {
  margin-bottom: 8px;
}

.message-text {
  background: var(--el-fill-color-light);
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-user .message-text {
  background: var(--el-color-primary);
  color: white;
}

.message-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-radius: 12px;
}

.message-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--el-color-error-light-9);
  color: var(--el-color-error);
  border-radius: 12px;
}

.message-data {
  background: var(--el-fill-color-light);
  padding: 16px;
  border-radius: 12px;
}

.data-title {
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: var(--el-text-color-placeholder);
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-8px);
  }
}

.loading-text {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.message-user .message-actions {
  justify-content: flex-end;
}

.typing-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.chat-input {
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
}

.suggestions {
  padding: 12px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.suggestions-title {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.suggestion-chip {
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-chip:hover {
  background: var(--el-color-primary);
  color: white;
}

.input-container {
  padding: 16px 20px;
}

.input-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.input-wrapper {
  position: relative;
}

.message-input {
  resize: none;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.char-count {
  color: var(--el-text-color-placeholder);
}

.input-hint {
  color: var(--el-text-color-placeholder);
}

.send-button {
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }
  
  .chat-messages {
    padding: 12px 16px;
  }
  
  .input-container {
    padding: 12px 16px;
  }
  
  .welcome-message {
    padding: 20px 16px;
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .input-info {
    justify-content: space-between;
  }
}
</style>
