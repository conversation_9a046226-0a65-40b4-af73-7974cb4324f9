import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { 
          title: '工作台',
          icon: 'House'
        }
      },
      {
        path: 'requirements',
        name: 'Requirements',
        component: () => import('@/views/Requirements/index.vue'),
        meta: { 
          title: '需求管理',
          icon: 'Document'
        },
        children: [
          {
            path: 'collection',
            name: 'RequirementCollection',
            component: () => import('@/views/Requirements/Collection.vue'),
            meta: { 
              title: '需求收集',
              breadcrumb: ['需求管理', '需求收集']
            }
          },
          {
            path: 'analysis',
            name: 'RequirementAnalysis',
            component: () => import('@/views/Requirements/Analysis.vue'),
            meta: { 
              title: '需求分析',
              breadcrumb: ['需求管理', '需求分析']
            }
          }
        ]
      },
      {
        path: 'solutions',
        name: 'Solutions',
        component: () => import('@/views/Solutions/index.vue'),
        meta: { 
          title: '方案管理',
          icon: 'Files'
        },
        children: [
          {
            path: 'generation',
            name: 'SolutionGeneration',
            component: () => import('@/views/Solutions/Generation.vue'),
            meta: { 
              title: '方案生成',
              breadcrumb: ['方案管理', '方案生成']
            }
          },
          {
            path: 'editing',
            name: 'SolutionEditing',
            component: () => import('@/views/Solutions/Editing.vue'),
            meta: { 
              title: '方案编辑',
              breadcrumb: ['方案管理', '方案编辑']
            }
          },
          {
            path: 'templates',
            name: 'SolutionTemplates',
            component: () => import('@/views/Solutions/Templates.vue'),
            meta: { 
              title: '方案模板',
              breadcrumb: ['方案管理', '方案模板']
            }
          }
        ]
      },
      {
        path: 'pricing',
        name: 'Pricing',
        component: () => import('@/views/Pricing/index.vue'),
        meta: { 
          title: '报价管理',
          icon: 'Money'
        },
        children: [
          {
            path: 'engine',
            name: 'PricingEngine',
            component: () => import('@/views/Pricing/Engine.vue'),
            meta: { 
              title: '报价引擎',
              breadcrumb: ['报价管理', '报价引擎']
            }
          },
          {
            path: 'optimization',
            name: 'PricingOptimization',
            component: () => import('@/views/Pricing/Optimization.vue'),
            meta: { 
              title: '预算优化',
              breadcrumb: ['报价管理', '预算优化']
            }
          },
          {
            path: 'negotiation',
            name: 'PricingNegotiation',
            component: () => import('@/views/Pricing/Negotiation.vue'),
            meta: { 
              title: '价格谈判',
              breadcrumb: ['报价管理', '价格谈判']
            }
          }
        ]
      },
      {
        path: 'contracts',
        name: 'Contracts',
        component: () => import('@/views/Contracts/index.vue'),
        meta: { 
          title: '合同管理',
          icon: 'Document'
        },
        children: [
          {
            path: 'generation',
            name: 'ContractGeneration',
            component: () => import('@/views/Contracts/Generation.vue'),
            meta: { 
              title: '合同生成',
              breadcrumb: ['合同管理', '合同生成']
            }
          },
          {
            path: 'review',
            name: 'ContractReview',
            component: () => import('@/views/Contracts/Review.vue'),
            meta: { 
              title: '合同审核',
              breadcrumb: ['合同管理', '合同审核']
            }
          }
        ]
      },
      {
        path: 'knowledge',
        name: 'Knowledge',
        component: () => import('@/views/Knowledge/index.vue'),
        meta: { 
          title: '知识库',
          icon: 'Collection'
        },
        children: [
          {
            path: 'products',
            name: 'ProductKnowledge',
            component: () => import('@/views/Knowledge/Products.vue'),
            meta: { 
              title: '产品库',
              breadcrumb: ['知识库', '产品库']
            }
          },
          {
            path: 'cases',
            name: 'CaseKnowledge',
            component: () => import('@/views/Knowledge/Cases.vue'),
            meta: { 
              title: '案例库',
              breadcrumb: ['知识库', '案例库']
            }
          },
          {
            path: 'pricing-rules',
            name: 'PricingRules',
            component: () => import('@/views/Knowledge/PricingRules.vue'),
            meta: { 
              title: '报价规则',
              breadcrumb: ['知识库', '报价规则']
            }
          }
        ]
      },
      {
        path: 'admin',
        name: 'Admin',
        component: () => import('@/views/Admin/index.vue'),
        meta: { 
          title: '系统管理',
          icon: 'Setting',
          roles: ['admin']
        },
        children: [
          {
            path: 'api-config',
            name: 'APIConfig',
            component: () => import('@/views/Admin/APIConfig.vue'),
            meta: { 
              title: 'API配置',
              breadcrumb: ['系统管理', 'API配置'],
              roles: ['admin']
            }
          },
          {
            path: 'users',
            name: 'UserManagement',
            component: () => import('@/views/Admin/Users.vue'),
            meta: { 
              title: '用户管理',
              breadcrumb: ['系统管理', '用户管理'],
              roles: ['admin']
            }
          },
          {
            path: 'roles',
            name: 'RoleManagement',
            component: () => import('@/views/Admin/Roles.vue'),
            meta: { 
              title: '角色管理',
              breadcrumb: ['系统管理', '角色管理'],
              roles: ['admin']
            }
          },
          {
            path: 'ai-constraints',
            name: 'AIConstraints',
            component: () => import('@/views/Admin/AIConstraints.vue'),
            meta: { 
              title: 'AI约束配置',
              breadcrumb: ['系统管理', 'AI约束配置'],
              roles: ['admin']
            }
          },
          {
            path: 'monitoring',
            name: 'SystemMonitoring',
            component: () => import('@/views/Admin/Monitoring.vue'),
            meta: { 
              title: '系统监控',
              breadcrumb: ['系统管理', '系统监控'],
              roles: ['admin']
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI智能销售CPQ系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!userStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }
    
    // 检查角色权限
    if (to.meta.roles && to.meta.roles.length > 0) {
      if (!userStore.hasAnyRole(to.meta.roles)) {
        ElMessage.error('没有访问权限')
        next({ name: 'Dashboard' })
        return
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.name === 'Login' && userStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }
  
  next()
})

export default router
