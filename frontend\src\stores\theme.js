import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const isDark = ref(false)
  const primaryColor = ref('#409EFF')
  const sidebarCollapsed = ref(false)
  const language = ref('zh-cn')

  // 初始化主题
  const initTheme = () => {
    // 从localStorage读取主题设置
    const savedTheme = localStorage.getItem('theme-settings')
    if (savedTheme) {
      const settings = JSON.parse(savedTheme)
      isDark.value = settings.isDark || false
      primaryColor.value = settings.primaryColor || '#409EFF'
      sidebarCollapsed.value = settings.sidebarCollapsed || false
      language.value = settings.language || 'zh-cn'
    }
    
    // 应用主题
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    const html = document.documentElement
    
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
    
    // 设置主色调
    html.style.setProperty('--el-color-primary', primaryColor.value)
    
    // 保存到localStorage
    saveThemeSettings()
  }

  // 保存主题设置
  const saveThemeSettings = () => {
    const settings = {
      isDark: isDark.value,
      primaryColor: primaryColor.value,
      sidebarCollapsed: sidebarCollapsed.value,
      language: language.value
    }
    localStorage.setItem('theme-settings', JSON.stringify(settings))
  }

  // 切换暗色模式
  const toggleDark = () => {
    isDark.value = !isDark.value
    applyTheme()
  }

  // 设置主色调
  const setPrimaryColor = (color) => {
    primaryColor.value = color
    applyTheme()
  }

  // 切换侧边栏折叠状态
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    saveThemeSettings()
  }

  // 设置语言
  const setLanguage = (lang) => {
    language.value = lang
    saveThemeSettings()
  }

  return {
    // 状态
    isDark,
    primaryColor,
    sidebarCollapsed,
    language,
    
    // 方法
    initTheme,
    applyTheme,
    toggleDark,
    setPrimaryColor,
    toggleSidebar,
    setLanguage
  }
})
