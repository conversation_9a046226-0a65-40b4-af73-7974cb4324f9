import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  const permissions = ref([])
  const roles = ref([])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => userInfo.value.name || '')
  const userRole = computed(() => userInfo.value.role || 'user')
  const avatar = computed(() => userInfo.value.avatar || '')

  // 方法
  const setToken = (newToken) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const setUserInfo = (info) => {
    userInfo.value = info
    localStorage.setItem('userInfo', JSON.stringify(info))
  }

  const setPermissions = (perms) => {
    permissions.value = perms
  }

  const setRoles = (userRoles) => {
    roles.value = userRoles
  }

  // 登录
  const login = async (loginForm) => {
    try {
      const response = await authAPI.login(loginForm)
      const { token: newToken, user, permissions: userPermissions, roles: userRoles } = response.data
      
      setToken(newToken)
      setUserInfo(user)
      setPermissions(userPermissions)
      setRoles(userRoles)
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = {}
      permissions.value = []
      roles.value = []
      
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      ElMessage.success('已退出登录')
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      const response = await authAPI.getUserInfo()
      const { user, permissions: userPermissions, roles: userRoles } = response.data
      
      setUserInfo(user)
      setPermissions(userPermissions)
      setRoles(userRoles)
      
      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      // 清除无效token
      logout()
      return false
    }
  }

  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role) => {
    return roles.value.includes(role)
  }

  // 检查是否有任一角色
  const hasAnyRole = (roleList) => {
    return roleList.some(role => roles.value.includes(role))
  }

  // 更新用户信息
  const updateUserInfo = async (updateData) => {
    try {
      const response = await authAPI.updateProfile(updateData)
      setUserInfo(response.data.user)
      ElMessage.success('用户信息更新成功')
      return response
    } catch (error) {
      ElMessage.error(error.message || '更新失败')
      throw error
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      await authAPI.changePassword(passwordData)
      ElMessage.success('密码修改成功，请重新登录')
      logout()
    } catch (error) {
      ElMessage.error(error.message || '密码修改失败')
      throw error
    }
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    roles,
    
    // 计算属性
    isAuthenticated,
    userName,
    userRole,
    avatar,
    
    // 方法
    setToken,
    setUserInfo,
    setPermissions,
    setRoles,
    login,
    logout,
    checkAuth,
    hasPermission,
    hasRole,
    hasAnyRole,
    updateUserInfo,
    changePassword
  }
})
