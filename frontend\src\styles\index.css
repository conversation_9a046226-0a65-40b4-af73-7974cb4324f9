/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 自定义CSS变量 */
:root {
  --app-header-height: 60px;
  --app-sidebar-width: 260px;
  --app-sidebar-collapsed-width: 64px;
  --app-border-radius: 8px;
  --app-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --app-transition: all 0.3s ease;
}

/* 暗色主题变量 */
.dark {
  --el-bg-color-page: #0a0a0a;
  --el-bg-color: #141414;
  --el-bg-color-overlay: #1d1d1d;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color: #414243;
  --el-border-color-light: #363637;
  --el-border-color-lighter: #2b2b2c;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-darker: #636466;
  --el-fill-color: #303133;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: transparent;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-2xl {
  font-size: 24px;
}

.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.text-warning {
  color: var(--el-color-warning);
}

.text-danger {
  color: var(--el-color-danger);
}

.text-info {
  color: var(--el-color-info);
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-green-500 {
  color: #10b981;
}

.text-orange-500 {
  color: #f59e0b;
}

.text-red-500 {
  color: #ef4444;
}

.bg-primary {
  background-color: var(--el-color-primary);
}

.bg-success {
  background-color: var(--el-color-success);
}

.bg-warning {
  background-color: var(--el-color-warning);
}

.bg-danger {
  background-color: var(--el-color-danger);
}

.bg-info {
  background-color: var(--el-color-info);
}

.rounded {
  border-radius: var(--app-border-radius);
}

.rounded-lg {
  border-radius: 12px;
}

.shadow {
  box-shadow: var(--app-box-shadow);
}

.transition {
  transition: var(--app-transition);
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-50 {
  z-index: 50;
}

/* 自定义组件样式 */
.page-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - var(--app-header-height));
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin: 4px 0 0 0;
}

.card-grid {
  display: grid;
  gap: 20px;
}

.card-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.card-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: var(--app-border-radius);
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-state-icon {
  font-size: 64px;
  color: var(--el-text-color-placeholder);
  margin-bottom: 16px;
}

.empty-state-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.empty-state-description {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
  margin-bottom: 24px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  color: var(--el-color-danger);
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 24px;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .card-grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 992px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .card-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .card-grid,
  .card-grid-2,
  .card-grid-3,
  .card-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .table-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-container {
    padding: 0;
    background: white;
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
