<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-illustration">
        <div class="error-number">404</div>
        <div class="error-icon">
          <el-icon><DocumentDelete /></el-icon>
        </div>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">页面不存在</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
          <br>
          请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
          
          <el-button @click="refresh">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-button>
        </div>
        
        <div class="help-links">
          <p class="help-title">您可能在寻找：</p>
          <div class="link-grid">
            <router-link to="/dashboard" class="help-link">
              <el-icon><House /></el-icon>
              <span>工作台</span>
            </router-link>
            
            <router-link to="/requirements/collection" class="help-link">
              <el-icon><Document /></el-icon>
              <span>需求收集</span>
            </router-link>
            
            <router-link to="/solutions/generation" class="help-link">
              <el-icon><Files /></el-icon>
              <span>方案生成</span>
            </router-link>
            
            <router-link to="/pricing/engine" class="help-link">
              <el-icon><Money /></el-icon>
              <span>智能报价</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>
    
    <div class="error-footer">
      <p>如果问题持续存在，请联系技术支持</p>
      <div class="contact-info">
        <span>邮箱：<EMAIL></span>
        <span>电话：400-123-4567</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import {
  DocumentDelete,
  House,
  Back,
  Refresh,
  Document,
  Files,
  Money
} from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const refresh = () => {
  window.location.reload()
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 20px;
}

.error-content {
  max-width: 600px;
  width: 100%;
}

.error-illustration {
  position: relative;
  margin-bottom: 40px;
}

.error-number {
  font-size: 120px;
  font-weight: 700;
  line-height: 1;
  opacity: 0.3;
  margin-bottom: -20px;
}

.error-icon {
  font-size: 80px;
  color: rgba(255, 255, 255, 0.8);
}

.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 32px 0;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.help-links {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  margin-top: 32px;
}

.help-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
  opacity: 0.9;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.help-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  text-decoration: none;
  transition: all 0.3s;
}

.help-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.help-link .el-icon {
  font-size: 24px;
}

.help-link span {
  font-size: 14px;
}

.error-footer {
  margin-top: 40px;
  opacity: 0.8;
}

.error-footer p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.contact-info {
  display: flex;
  gap: 20px;
  justify-content: center;
  font-size: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-number {
    font-size: 80px;
  }
  
  .error-icon {
    font-size: 60px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-description {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 200px;
  }
  
  .link-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .contact-info {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .error-page {
    padding: 16px;
  }
  
  .help-links {
    padding: 16px;
  }
  
  .link-grid {
    grid-template-columns: 1fr;
  }
}
</style>
