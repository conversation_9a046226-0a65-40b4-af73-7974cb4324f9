<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎回来，{{ userStore.userName }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，让我们开始高效的销售工作吧
        </p>
      </div>
      <div class="welcome-actions">
        <el-button type="primary" size="large" @click="startNewProject">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
        <el-button size="large" @click="viewReports">
          <el-icon><DataAnalysis /></el-icon>
          查看报表
        </el-button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalProjects }}</div>
          <div class="stat-label">总项目数</div>
          <div class="stat-change positive">
            <el-icon><ArrowUp /></el-icon>
            +12%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatNumber(stats.totalRevenue) }}</div>
          <div class="stat-label">总成交金额</div>
          <div class="stat-change positive">
            <el-icon><ArrowUp /></el-icon>
            +8.5%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.activeCustomers }}</div>
          <div class="stat-label">活跃客户</div>
          <div class="stat-change positive">
            <el-icon><ArrowUp /></el-icon>
            +15%
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.conversionRate }}%</div>
          <div class="stat-label">转化率</div>
          <div class="stat-change negative">
            <el-icon><ArrowDown /></el-icon>
            -2.1%
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容 -->
      <div class="left-content">
        <!-- 最近项目 -->
        <el-card class="recent-projects">
          <template #header>
            <div class="card-header">
              <span>最近项目</span>
              <el-button text @click="$router.push('/projects')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="project-list">
            <div
              v-for="project in recentProjects"
              :key="project.id"
              class="project-item"
              @click="openProject(project)"
            >
              <div class="project-info">
                <div class="project-name">{{ project.name }}</div>
                <div class="project-customer">{{ project.customer }}</div>
                <div class="project-meta">
                  <span class="project-date">{{ formatDate(project.updatedAt) }}</span>
                  <el-tag :type="getStatusType(project.status)" size="small">
                    {{ getStatusText(project.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="project-amount">
                ¥{{ formatNumber(project.amount) }}
              </div>
            </div>
          </div>
        </el-card>

        <!-- 销售漏斗 -->
        <el-card class="sales-funnel">
          <template #header>
            <span>销售漏斗</span>
          </template>
          
          <div class="funnel-chart" ref="funnelChartRef"></div>
        </el-card>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 待办事项 -->
        <el-card class="todo-list">
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <el-badge :value="todoList.length" type="primary" />
            </div>
          </template>
          
          <div class="todo-items">
            <div
              v-for="todo in todoList"
              :key="todo.id"
              class="todo-item"
              :class="{ completed: todo.completed }"
            >
              <el-checkbox
                v-model="todo.completed"
                @change="updateTodo(todo)"
              />
              <div class="todo-content">
                <div class="todo-title">{{ todo.title }}</div>
                <div class="todo-meta">
                  <span class="todo-date">{{ formatDate(todo.dueDate) }}</span>
                  <el-tag :type="getPriorityType(todo.priority)" size="small">
                    {{ todo.priority }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          
          <el-button text class="add-todo-btn" @click="showAddTodo = true">
            <el-icon><Plus /></el-icon>
            添加待办
          </el-button>
        </el-card>

        <!-- 快速操作 -->
        <el-card class="quick-actions">
          <template #header>
            <span>快速操作</span>
          </template>
          
          <div class="action-grid">
            <div class="action-item" @click="$router.push('/requirements/collection')">
              <el-icon><EditPen /></el-icon>
              <span>需求收集</span>
            </div>
            
            <div class="action-item" @click="$router.push('/solutions/generation')">
              <el-icon><Document /></el-icon>
              <span>生成方案</span>
            </div>
            
            <div class="action-item" @click="$router.push('/pricing/engine')">
              <el-icon><Money /></el-icon>
              <span>智能报价</span>
            </div>
            
            <div class="action-item" @click="$router.push('/contracts/generation')">
              <el-icon><Files /></el-icon>
              <span>生成合同</span>
            </div>
          </div>
        </el-card>

        <!-- 系统通知 -->
        <el-card class="notifications">
          <template #header>
            <span>系统通知</span>
          </template>
          
          <div class="notification-list">
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="notification-item"
            >
              <div class="notification-icon">
                <el-icon :class="getNotificationIconClass(notification.type)">
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加待办对话框 -->
    <el-dialog v-model="showAddTodo" title="添加待办事项" width="400px">
      <el-form :model="newTodo" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="newTodo.title" placeholder="请输入待办事项" />
        </el-form-item>
        <el-form-item label="截止日期">
          <el-date-picker
            v-model="newTodo.dueDate"
            type="datetime"
            placeholder="选择截止日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="newTodo.priority" style="width: 100%">
            <el-option label="高" value="高" />
            <el-option label="中" value="中" />
            <el-option label="低" value="低" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddTodo = false">取消</el-button>
        <el-button type="primary" @click="addTodo">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import {
  Plus,
  DataAnalysis,
  Document,
  Money,
  User,
  TrendCharts,
  ArrowUp,
  ArrowDown,
  EditPen,
  Files,
  Bell,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showAddTodo = ref(false)
const funnelChartRef = ref()

// 当前日期
const currentDate = dayjs().format('YYYY年MM月DD日 dddd')

// 统计数据
const stats = reactive({
  totalProjects: 156,
  totalRevenue: 2580000,
  activeCustomers: 89,
  conversionRate: 68.5
})

// 最近项目
const recentProjects = ref([
  {
    id: 1,
    name: '企业数字化转型项目',
    customer: '科技有限公司',
    amount: 580000,
    status: 'in_progress',
    updatedAt: '2025-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'ERP系统升级',
    customer: '制造集团',
    amount: 320000,
    status: 'pending',
    updatedAt: '2025-01-14T16:20:00Z'
  },
  {
    id: 3,
    name: '智能仓储解决方案',
    customer: '物流公司',
    amount: 750000,
    status: 'completed',
    updatedAt: '2025-01-13T09:15:00Z'
  }
])

// 待办事项
const todoList = ref([
  {
    id: 1,
    title: '完成ABC公司需求分析报告',
    dueDate: '2025-01-16T18:00:00Z',
    priority: '高',
    completed: false
  },
  {
    id: 2,
    title: '准备XYZ项目演示材料',
    dueDate: '2025-01-17T14:00:00Z',
    priority: '中',
    completed: false
  },
  {
    id: 3,
    title: '回复客户邮件',
    dueDate: '2025-01-15T17:00:00Z',
    priority: '高',
    completed: true
  }
])

// 新待办事项
const newTodo = reactive({
  title: '',
  dueDate: '',
  priority: '中'
})

// 系统通知
const notifications = ref([
  {
    id: 1,
    type: 'info',
    title: '系统将于今晚22:00进行维护',
    createdAt: '2025-01-15T14:30:00Z'
  },
  {
    id: 2,
    type: 'warning',
    title: 'API调用量接近限制',
    createdAt: '2025-01-15T12:15:00Z'
  },
  {
    id: 3,
    type: 'success',
    title: '新版本功能已上线',
    createdAt: '2025-01-15T09:00:00Z'
  }
])

// 方法
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const formatDate = (date) => {
  return dayjs(date).format('MM-DD HH:mm')
}

const formatTime = (date) => {
  return dayjs(date).fromNow()
}

const getStatusType = (status) => {
  const types = {
    completed: 'success',
    in_progress: 'warning',
    pending: 'info',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    in_progress: '进行中',
    pending: '待开始',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority) => {
  const types = {
    高: 'danger',
    中: 'warning',
    低: 'info'
  }
  return types[priority] || 'info'
}

const getNotificationIcon = (type) => {
  const icons = {
    info: InfoFilled,
    warning: Warning,
    success: Bell
  }
  return icons[type] || InfoFilled
}

const getNotificationIconClass = (type) => {
  const classes = {
    info: 'text-blue-500',
    warning: 'text-orange-500',
    success: 'text-green-500'
  }
  return classes[type] || 'text-gray-500'
}

const startNewProject = () => {
  router.push('/requirements/collection')
}

const viewReports = () => {
  router.push('/reports')
}

const openProject = (project) => {
  router.push(`/projects/${project.id}`)
}

const updateTodo = (todo) => {
  ElMessage.success(todo.completed ? '任务已完成' : '任务已重新激活')
}

const addTodo = () => {
  if (!newTodo.title.trim()) {
    ElMessage.warning('请输入待办事项标题')
    return
  }
  
  const todo = {
    id: Date.now(),
    title: newTodo.title,
    dueDate: newTodo.dueDate || new Date().toISOString(),
    priority: newTodo.priority,
    completed: false
  }
  
  todoList.value.unshift(todo)
  
  // 重置表单
  newTodo.title = ''
  newTodo.dueDate = ''
  newTodo.priority = '中'
  
  showAddTodo.value = false
  ElMessage.success('待办事项添加成功')
}

// 初始化销售漏斗图表
const initFunnelChart = () => {
  if (!funnelChartRef.value) return
  
  const chart = echarts.init(funnelChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: [
          { value: 100, name: '潜在客户' },
          { value: 80, name: '初步接触' },
          { value: 60, name: '需求确认' },
          { value: 40, name: '方案提交' },
          { value: 20, name: '商务谈判' },
          { value: 15, name: '成功签约' }
        ]
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initFunnelChart()
  })
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.left-content,
.right-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.project-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-fill-color-extra-light);
}

.project-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.project-customer {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-date {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.project-amount {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.funnel-chart {
  height: 300px;
}

.todo-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.todo-item:hover {
  background-color: var(--el-fill-color-extra-light);
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item.completed .todo-title {
  text-decoration: line-through;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-date {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.add-todo-btn {
  width: 100%;
  justify-content: center;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-fill-color-extra-light);
}

.action-item .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.action-item span {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: var(--el-fill-color-extra-light);
}

.notification-icon {
  font-size: 16px;
  margin-top: 2px;
}

.notification-title {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .welcome-section {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .welcome-section {
    padding: 24px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
}
</style>
