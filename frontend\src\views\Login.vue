<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-animation"></div>
    </div>
    
    <div class="login-content">
      <div class="login-form-container">
        <div class="login-header">
          <div class="logo">
            <img src="/logo.svg" alt="Logo" class="logo-img" />
            <h1 class="logo-text">AI智能销售CPQ系统</h1>
          </div>
          <p class="subtitle">AI驱动的智能销售配置定价报价系统</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名/邮箱"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="captcha" v-if="showCaptcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                prefix-icon="Key"
                clearable
                style="flex: 1"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="验证码" />
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">
                记住我
              </el-checkbox>
              <el-link type="primary" @click="showForgotPassword = true">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <div class="quick-login">
            <el-divider>快速登录</el-divider>
            <div class="demo-accounts">
              <el-button
                v-for="account in demoAccounts"
                :key="account.role"
                size="small"
                @click="quickLogin(account)"
              >
                {{ account.name }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="login-features">
        <h3>系统特色</h3>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon class="feature-icon"><Robot /></el-icon>
            <div class="feature-content">
              <h4>AI智能助手</h4>
              <p>多轮对话收集需求，自动生成解决方案</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon class="feature-icon"><Money /></el-icon>
            <div class="feature-content">
              <h4>智能报价引擎</h4>
              <p>自动匹配产品价格，支持预算优化</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon class="feature-icon"><Document /></el-icon>
            <div class="feature-content">
              <h4>文档自动生成</h4>
              <p>一键生成需求文档、方案、合同</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon class="feature-icon"><DataAnalysis /></el-icon>
            <div class="feature-content">
              <h4>数据智能分析</h4>
              <p>销售数据分析，知识沉淀复用</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="showForgotPassword"
      title="忘记密码"
      width="400px"
      @close="resetForgotForm"
    >
      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        label-width="80px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="forgotForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showForgotPassword = false">取消</el-button>
        <el-button
          type="primary"
          :loading="forgotLoading"
          @click="handleForgotPassword"
        >
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Robot, Money, Document, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const forgotLoading = ref(false)
const showCaptcha = ref(false)
const showForgotPassword = ref(false)
const captchaUrl = ref('')
const loginFormRef = ref()
const forgotFormRef = ref()

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 忘记密码表单
const forgotForm = reactive({
  email: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为4位', trigger: 'blur' }
  ]
}

const forgotRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 演示账号
const demoAccounts = [
  { role: 'admin', name: '管理员', username: 'admin', password: 'admin123' },
  { role: 'sales', name: '销售员', username: 'sales', password: 'sales123' },
  { role: 'consultant', name: '顾问', username: 'consultant', password: 'consultant123' },
  { role: 'customer', name: '客户', username: 'customer', password: 'customer123' }
]

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    await userStore.login(loginForm)
    
    // 登录成功后跳转
    const redirect = route.query.redirect || '/'
    router.push(redirect)
    
  } catch (error) {
    console.error('登录失败:', error)
    // 登录失败后显示验证码
    if (!showCaptcha.value) {
      showCaptcha.value = true
      refreshCaptcha()
    }
  } finally {
    loading.value = false
  }
}

// 快速登录
const quickLogin = (account) => {
  loginForm.username = account.username
  loginForm.password = account.password
  handleLogin()
}

// 处理忘记密码
const handleForgotPassword = async () => {
  if (!forgotFormRef.value) return
  
  try {
    await forgotFormRef.value.validate()
    forgotLoading.value = true
    
    // 调用忘记密码API
    // await authAPI.forgotPassword(forgotForm)
    
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    showForgotPassword.value = false
    
  } catch (error) {
    console.error('发送重置邮件失败:', error)
  } finally {
    forgotLoading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaUrl.value = `/api/auth/captcha?t=${Date.now()}`
}

// 重置忘记密码表单
const resetForgotForm = () => {
  forgotForm.email = ''
  if (forgotFormRef.value) {
    forgotFormRef.value.resetFields()
  }
}

// 组件挂载
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.login-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  gap: 60px;
}

.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-img {
  width: 48px;
  height: 48px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.subtitle {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-image {
  width: 100px;
  height: 40px;
  cursor: pointer;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  margin-top: 20px;
}

.demo-accounts {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.login-features {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  color: white;
}

.login-features h3 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 600;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.feature-icon {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.feature-content p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-content {
    flex-direction: column;
    gap: 40px;
  }
  
  .login-features {
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .login-content {
    padding: 20px 16px;
  }
  
  .login-form-container,
  .login-features {
    max-width: 100%;
    padding: 30px 20px;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .feature-list {
    gap: 20px;
  }
}
</style>
