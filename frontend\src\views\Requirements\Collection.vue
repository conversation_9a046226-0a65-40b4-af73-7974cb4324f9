<template>
  <div class="requirement-collection">
    <div class="page-header">
      <div>
        <h1 class="page-title">AI智能需求收集</h1>
        <p class="page-subtitle">通过AI对话快速收集和整理客户需求</p>
      </div>
      <div class="header-actions">
        <el-button @click="showTemplates = true">
          <el-icon><Document /></el-icon>
          使用模板
        </el-button>
        <el-button type="primary" @click="startNewCollection">
          <el-icon><Plus /></el-icon>
          新建收集
        </el-button>
      </div>
    </div>

    <div class="collection-container">
      <!-- 左侧：AI对话区域 -->
      <div class="chat-section">
        <el-card class="chat-card">
          <template #header>
            <div class="chat-header">
              <div class="chat-title">
                <el-icon><Robot /></el-icon>
                AI需求收集助手
              </div>
              <div class="chat-status">
                <el-tag :type="chatStatus === 'active' ? 'success' : 'info'" size="small">
                  {{ chatStatus === 'active' ? '对话中' : '待开始' }}
                </el-tag>
              </div>
            </div>
          </template>

          <!-- 对话消息列表 -->
          <div class="chat-messages" ref="chatMessagesRef">
            <div
              v-for="message in messages"
              :key="message.id"
              class="message-item"
              :class="{ 'message-user': message.role === 'user', 'message-ai': message.role === 'assistant' }"
            >
              <div class="message-avatar">
                <el-avatar :size="32">
                  <el-icon v-if="message.role === 'assistant'">
                    <Robot />
                  </el-icon>
                  <span v-else>{{ userStore.userName.charAt(0) }}</span>
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="message-sender">
                    {{ message.role === 'assistant' ? 'AI助手' : userStore.userName }}
                  </span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-text" v-html="formatMessage(message.content)"></div>
                
                <!-- AI消息的操作按钮 -->
                <div v-if="message.role === 'assistant'" class="message-actions">
                  <el-button text size="small" @click="copyMessage(message.content)">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-button>
                  <el-button text size="small" @click="regenerateResponse(message)">
                    <el-icon><Refresh /></el-icon>
                    重新生成
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isTyping" class="message-item message-ai">
              <div class="message-avatar">
                <el-avatar :size="32">
                  <el-icon><Robot /></el-icon>
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <div class="input-toolbar">
              <el-button text size="small" @click="showSuggestions = !showSuggestions">
                <el-icon><ChatDotRound /></el-icon>
                建议问题
              </el-button>
              <el-button text size="small" @click="clearChat">
                <el-icon><Delete /></el-icon>
                清空对话
              </el-button>
            </div>
            
            <!-- 建议问题 -->
            <div v-if="showSuggestions" class="suggestions">
              <div class="suggestions-title">常用问题模板：</div>
              <div class="suggestion-tags">
                <el-tag
                  v-for="suggestion in suggestions"
                  :key="suggestion"
                  class="suggestion-tag"
                  @click="sendSuggestion(suggestion)"
                >
                  {{ suggestion }}
                </el-tag>
              </div>
            </div>

            <div class="input-container">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="3"
                placeholder="请描述您的业务需求，AI助手将帮助您整理和完善..."
                @keydown.ctrl.enter="sendMessage"
                :disabled="isTyping"
              />
              <div class="input-actions">
                <span class="input-hint">Ctrl + Enter 发送</span>
                <el-button
                  type="primary"
                  :loading="isTyping"
                  :disabled="!inputMessage.trim()"
                  @click="sendMessage"
                >
                  发送
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：需求整理区域 -->
      <div class="requirement-section">
        <el-card class="requirement-card">
          <template #header>
            <div class="requirement-header">
              <div class="requirement-title">
                <el-icon><DocumentChecked /></el-icon>
                需求整理
              </div>
              <div class="requirement-actions">
                <el-button text size="small" @click="exportRequirement">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
                <el-button text size="small" @click="saveRequirement">
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
              </div>
            </div>
          </template>

          <!-- 需求概览 -->
          <div class="requirement-overview">
            <div class="overview-stats">
              <div class="stat-item">
                <span class="stat-label">收集进度</span>
                <el-progress :percentage="collectionProgress" :stroke-width="8" />
              </div>
              <div class="stat-item">
                <span class="stat-label">完整度</span>
                <el-progress :percentage="completeness" :stroke-width="8" color="#67c23a" />
              </div>
            </div>
          </div>

          <!-- 需求分类 -->
          <div class="requirement-categories">
            <el-collapse v-model="activeCategories">
              <el-collapse-item
                v-for="category in requirementCategories"
                :key="category.key"
                :name="category.key"
              >
                <template #title>
                  <div class="category-title">
                    <el-icon><component :is="category.icon" /></el-icon>
                    <span>{{ category.title }}</span>
                    <el-badge
                      :value="category.items.length"
                      :hidden="category.items.length === 0"
                      type="primary"
                    />
                  </div>
                </template>

                <div class="category-content">
                  <div
                    v-for="item in category.items"
                    :key="item.id"
                    class="requirement-item"
                  >
                    <div class="item-content">
                      <div class="item-title">{{ item.title }}</div>
                      <div class="item-description">{{ item.description }}</div>
                    </div>
                    <div class="item-actions">
                      <el-button text size="small" @click="editRequirementItem(item)">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button text size="small" type="danger" @click="deleteRequirementItem(item)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 添加新需求项 -->
                  <el-button
                    text
                    class="add-item-btn"
                    @click="addRequirementItem(category.key)"
                  >
                    <el-icon><Plus /></el-icon>
                    添加{{ category.title }}
                  </el-button>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 需求模板对话框 -->
    <el-dialog v-model="showTemplates" title="选择需求模板" width="800px">
      <div class="template-grid">
        <div
          v-for="template in requirementTemplates"
          :key="template.id"
          class="template-card"
          @click="useTemplate(template)"
        >
          <div class="template-icon">
            <el-icon><component :is="template.icon" /></el-icon>
          </div>
          <div class="template-content">
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
            <div class="template-tags">
              <el-tag
                v-for="tag in template.tags"
                :key="tag"
                size="small"
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑需求项对话框 -->
    <el-dialog v-model="showEditItem" title="编辑需求项" width="500px">
      <el-form :model="editingItem" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="editingItem.title" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editingItem.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="editingItem.priority">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditItem = false">取消</el-button>
        <el-button type="primary" @click="saveRequirementItem">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Document,
  Plus,
  Robot,
  CopyDocument,
  Refresh,
  ChatDotRound,
  Delete,
  DocumentChecked,
  Download,
  Check,
  Edit,
  TrendCharts,
  Money,
  User,
  Setting,
  Timer
} from '@element-plus/icons-vue'

const userStore = useUserStore()

// 响应式数据
const chatMessagesRef = ref()
const inputMessage = ref('')
const isTyping = ref(false)
const chatStatus = ref('waiting')
const showSuggestions = ref(false)
const showTemplates = ref(false)
const showEditItem = ref(false)
const activeCategories = ref(['business', 'technical', 'budget'])

// 对话消息
const messages = ref([
  {
    id: 1,
    role: 'assistant',
    content: '您好！我是AI需求收集助手。我将帮助您收集和整理业务需求。请告诉我您的项目背景和主要目标。',
    timestamp: new Date().toISOString()
  }
])

// 建议问题
const suggestions = [
  '我们公司想要数字化转型',
  '需要一个ERP系统',
  '希望提升销售效率',
  '想要自动化业务流程',
  '需要数据分析平台',
  '希望改善客户体验'
]

// 需求分类
const requirementCategories = reactive([
  {
    key: 'business',
    title: '业务需求',
    icon: 'TrendCharts',
    items: []
  },
  {
    key: 'technical',
    title: '技术需求',
    icon: 'Setting',
    items: []
  },
  {
    key: 'budget',
    title: '预算需求',
    icon: 'Money',
    items: []
  },
  {
    key: 'timeline',
    title: '时间需求',
    icon: 'Timer',
    items: []
  }
])

// 需求模板
const requirementTemplates = [
  {
    id: 1,
    name: 'ERP系统需求',
    description: '企业资源规划系统的标准需求模板',
    icon: 'Setting',
    tags: ['ERP', '企业管理', '流程优化']
  },
  {
    id: 2,
    name: 'CRM系统需求',
    description: '客户关系管理系统的需求模板',
    icon: 'User',
    tags: ['CRM', '客户管理', '销售']
  },
  {
    id: 3,
    name: '数据分析平台',
    description: '商业智能和数据分析平台需求',
    icon: 'TrendCharts',
    tags: ['BI', '数据分析', '报表']
  }
]

// 编辑中的需求项
const editingItem = reactive({
  id: null,
  title: '',
  description: '',
  priority: 'medium',
  category: ''
})

// 计算属性
const collectionProgress = computed(() => {
  const totalCategories = requirementCategories.length
  const filledCategories = requirementCategories.filter(cat => cat.items.length > 0).length
  return Math.round((filledCategories / totalCategories) * 100)
})

const completeness = computed(() => {
  const totalItems = requirementCategories.reduce((sum, cat) => sum + cat.items.length, 0)
  return Math.min(totalItems * 10, 100)
})

// 方法
const formatTime = (timestamp) => {
  return dayjs(timestamp).format('HH:mm')
}

const formatMessage = (content) => {
  // 简单的markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isTyping.value) return

  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: inputMessage.value,
    timestamp: new Date().toISOString()
  }

  messages.value.push(userMessage)
  const userInput = inputMessage.value
  inputMessage.value = ''
  chatStatus.value = 'active'
  isTyping.value = true

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 模拟AI响应
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const aiResponse = await generateAIResponse(userInput)
    
    const aiMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: aiResponse,
      timestamp: new Date().toISOString()
    }

    messages.value.push(aiMessage)
    
    // 解析AI响应并更新需求
    parseAIResponse(aiResponse)
    
  } catch (error) {
    ElMessage.error('AI响应失败，请重试')
  } finally {
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

const sendSuggestion = (suggestion) => {
  inputMessage.value = suggestion
  showSuggestions.value = false
  sendMessage()
}

const generateAIResponse = async (userInput) => {
  // 模拟AI响应生成
  const responses = [
    '感谢您的信息！基于您的描述，我理解您需要一个数字化转型解决方案。让我为您整理一下关键需求点：\n\n**业务目标**：提升运营效率\n**技术要求**：云端部署\n**预算范围**：待确认\n\n请问您的具体行业是什么？这将帮助我提供更精准的建议。',
    '很好！我已经记录了您的需求。现在让我们深入了解一些细节：\n\n1. 您希望这个系统支持多少用户？\n2. 是否需要移动端支持？\n3. 对数据安全有什么特殊要求？',
    '根据您提供的信息，我建议我们重点关注以下几个方面：\n\n**功能模块**：用户管理、数据分析、报表生成\n**性能要求**：支持1000+并发用户\n**集成需求**：与现有系统对接\n\n还有其他重要的需求吗？'
  ]
  
  return responses[Math.floor(Math.random() * responses.length)]
}

const parseAIResponse = (response) => {
  // 简单的需求解析逻辑
  if (response.includes('业务目标') || response.includes('运营效率')) {
    addParsedRequirement('business', '提升运营效率', '通过数字化手段提升整体运营效率')
  }
  
  if (response.includes('技术要求') || response.includes('云端')) {
    addParsedRequirement('technical', '云端部署', '系统需要支持云端部署和扩展')
  }
  
  if (response.includes('预算') || response.includes('成本')) {
    addParsedRequirement('budget', '预算规划', '需要制定合理的项目预算方案')
  }
}

const addParsedRequirement = (category, title, description) => {
  const categoryObj = requirementCategories.find(cat => cat.key === category)
  if (categoryObj) {
    const exists = categoryObj.items.some(item => item.title === title)
    if (!exists) {
      categoryObj.items.push({
        id: Date.now() + Math.random(),
        title,
        description,
        priority: 'medium'
      })
    }
  }
}

const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
  }
}

const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const regenerateResponse = (message) => {
  ElMessage.info('重新生成功能开发中')
}

const clearChat = async () => {
  try {
    await ElMessageBox.confirm('确定要清空对话记录吗？', '确认操作', {
      type: 'warning'
    })
    
    messages.value = [messages.value[0]] // 保留欢迎消息
    chatStatus.value = 'waiting'
    ElMessage.success('对话已清空')
  } catch (error) {
    // 用户取消
  }
}

const startNewCollection = () => {
  // 重置所有数据
  messages.value = [messages.value[0]]
  requirementCategories.forEach(cat => {
    cat.items = []
  })
  chatStatus.value = 'waiting'
  ElMessage.success('已开始新的需求收集')
}

const useTemplate = (template) => {
  ElMessage.success(`已应用${template.name}模板`)
  showTemplates.value = false
  
  // 根据模板添加预设需求
  if (template.id === 1) { // ERP模板
    addParsedRequirement('business', '流程标准化', 'ERP系统需要标准化业务流程')
    addParsedRequirement('technical', '模块化设计', '系统需要支持模块化配置')
    addParsedRequirement('budget', 'ROI评估', '需要评估ERP系统的投资回报率')
  }
}

const addRequirementItem = (categoryKey) => {
  editingItem.id = null
  editingItem.title = ''
  editingItem.description = ''
  editingItem.priority = 'medium'
  editingItem.category = categoryKey
  showEditItem.value = true
}

const editRequirementItem = (item) => {
  editingItem.id = item.id
  editingItem.title = item.title
  editingItem.description = item.description
  editingItem.priority = item.priority
  editingItem.category = requirementCategories.find(cat => 
    cat.items.some(i => i.id === item.id)
  )?.key
  showEditItem.value = true
}

const saveRequirementItem = () => {
  if (!editingItem.title.trim()) {
    ElMessage.warning('请输入需求标题')
    return
  }

  const category = requirementCategories.find(cat => cat.key === editingItem.category)
  if (!category) return

  if (editingItem.id) {
    // 编辑现有项
    const item = category.items.find(item => item.id === editingItem.id)
    if (item) {
      item.title = editingItem.title
      item.description = editingItem.description
      item.priority = editingItem.priority
    }
  } else {
    // 添加新项
    category.items.push({
      id: Date.now(),
      title: editingItem.title,
      description: editingItem.description,
      priority: editingItem.priority
    })
  }

  showEditItem.value = false
  ElMessage.success('需求项保存成功')
}

const deleteRequirementItem = async (item) => {
  try {
    await ElMessageBox.confirm('确定要删除这个需求项吗？', '确认删除', {
      type: 'warning'
    })
    
    const category = requirementCategories.find(cat => 
      cat.items.some(i => i.id === item.id)
    )
    if (category) {
      const index = category.items.findIndex(i => i.id === item.id)
      if (index > -1) {
        category.items.splice(index, 1)
        ElMessage.success('需求项已删除')
      }
    }
  } catch (error) {
    // 用户取消
  }
}

const saveRequirement = () => {
  ElMessage.success('需求已保存')
}

const exportRequirement = () => {
  ElMessage.info('导出功能开发中')
}

// 组件挂载
onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.requirement-collection {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.collection-container {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  overflow: hidden;
}

.chat-section,
.requirement-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-card,
.requirement-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header,
.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title,
.requirement-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 400px);
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 44px);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-sender {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.message-time {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.message-text {
  background: var(--el-fill-color-light);
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-user .message-text {
  background: var(--el-color-primary);
  color: white;
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-radius: 12px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: var(--el-text-color-placeholder);
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.chat-input {
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 16px;
}

.input-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.suggestions {
  margin-bottom: 12px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
}

.suggestions-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-tag:hover {
  background: var(--el-color-primary);
  color: white;
}

.input-container {
  position: relative;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-hint {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.requirement-overview {
  margin-bottom: 20px;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.requirement-categories {
  flex: 1;
  overflow-y: auto;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.category-content {
  padding: 12px 0;
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.requirement-item:hover {
  border-color: var(--el-color-primary);
  background: var(--el-fill-color-extra-light);
}

.item-content {
  flex: 1;
}

.item-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.item-description {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.add-item-btn {
  width: 100%;
  justify-content: center;
  margin-top: 8px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  margin-bottom: 12px;
}

.template-content h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.template-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .collection-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
  
  .requirement-section {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}
</style>
