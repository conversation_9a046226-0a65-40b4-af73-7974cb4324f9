<template>
  <div class="solution-generation">
    <div class="page-header">
      <div>
        <h1 class="page-title">AI智能方案生成</h1>
        <p class="page-subtitle">基于需求自动生成专业解决方案</p>
      </div>
      <div class="header-actions">
        <el-button @click="loadRequirements">
          <el-icon><Refresh /></el-icon>
          刷新需求
        </el-button>
        <el-button type="primary" @click="generateSolution" :loading="generating">
          <el-icon><Magic /></el-icon>
          {{ generating ? '生成中...' : '生成方案' }}
        </el-button>
      </div>
    </div>

    <div class="generation-container">
      <!-- 左侧：需求输入区域 -->
      <div class="requirements-section">
        <el-card class="requirements-card">
          <template #header>
            <div class="section-header">
              <div class="section-title">
                <el-icon><Document /></el-icon>
                需求信息
              </div>
              <el-button text size="small" @click="showRequirementSelector = true">
                <el-icon><Plus /></el-icon>
                选择需求
              </el-button>
            </div>
          </template>

          <!-- 需求概览 -->
          <div class="requirement-overview">
            <div class="overview-item">
              <span class="overview-label">项目名称：</span>
              <el-input v-model="projectInfo.name" placeholder="请输入项目名称" />
            </div>
            <div class="overview-item">
              <span class="overview-label">客户信息：</span>
              <el-input v-model="projectInfo.customer" placeholder="请输入客户名称" />
            </div>
            <div class="overview-item">
              <span class="overview-label">行业类型：</span>
              <el-select v-model="projectInfo.industry" placeholder="请选择行业">
                <el-option label="制造业" value="manufacturing" />
                <el-option label="金融服务" value="finance" />
                <el-option label="零售电商" value="retail" />
                <el-option label="教育培训" value="education" />
                <el-option label="医疗健康" value="healthcare" />
                <el-option label="其他" value="other" />
              </el-select>
            </div>
          </div>

          <!-- 需求列表 -->
          <div class="requirements-list">
            <div class="list-header">
              <span>已收集需求</span>
              <el-tag type="info" size="small">{{ selectedRequirements.length }} 项</el-tag>
            </div>
            
            <div class="requirement-items">
              <div
                v-for="req in selectedRequirements"
                :key="req.id"
                class="requirement-item"
              >
                <div class="req-content">
                  <div class="req-title">{{ req.title }}</div>
                  <div class="req-description">{{ req.description }}</div>
                  <div class="req-meta">
                    <el-tag :type="getPriorityType(req.priority)" size="small">
                      {{ req.priority }}
                    </el-tag>
                    <span class="req-category">{{ getCategoryName(req.category) }}</span>
                  </div>
                </div>
                <el-button
                  text
                  size="small"
                  type="danger"
                  @click="removeRequirement(req.id)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>

            <div v-if="selectedRequirements.length === 0" class="empty-requirements">
              <el-empty description="暂无需求信息">
                <el-button type="primary" @click="showRequirementSelector = true">
                  选择需求
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>

        <!-- 生成配置 -->
        <el-card class="generation-config">
          <template #header>
            <div class="section-title">
              <el-icon><Setting /></el-icon>
              生成配置
            </div>
          </template>

          <el-form :model="generationConfig" label-width="100px">
            <el-form-item label="方案类型">
              <el-select v-model="generationConfig.type" style="width: 100%">
                <el-option label="技术方案" value="technical" />
                <el-option label="商业方案" value="business" />
                <el-option label="综合方案" value="comprehensive" />
              </el-select>
            </el-form-item>

            <el-form-item label="详细程度">
              <el-radio-group v-model="generationConfig.detail">
                <el-radio label="简要">概要版</el-radio>
                <el-radio label="标准">标准版</el-radio>
                <el-radio label="详细">详细版</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="包含模块">
              <el-checkbox-group v-model="generationConfig.modules">
                <el-checkbox label="background">项目背景</el-checkbox>
                <el-checkbox label="analysis">需求分析</el-checkbox>
                <el-checkbox label="solution">解决方案</el-checkbox>
                <el-checkbox label="implementation">实施计划</el-checkbox>
                <el-checkbox label="budget">预算估算</el-checkbox>
                <el-checkbox label="risk">风险评估</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="AI模型">
              <el-select v-model="generationConfig.model" style="width: 100%">
                <el-option label="GPT-4 (推荐)" value="gpt-4" />
                <el-option label="Claude-3" value="claude-3" />
                <el-option label="DeepSeek" value="deepseek" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 右侧：方案生成区域 -->
      <div class="solution-section">
        <el-card class="solution-card">
          <template #header>
            <div class="section-header">
              <div class="section-title">
                <el-icon><DocumentChecked /></el-icon>
                生成的方案
              </div>
              <div class="solution-actions" v-if="generatedSolution">
                <el-button text size="small" @click="copySolution">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-button>
                <el-button text size="small" @click="exportSolution">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
                <el-button text size="small" @click="editSolution">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
              </div>
            </div>
          </template>

          <!-- 生成进度 -->
          <div v-if="generating" class="generation-progress">
            <div class="progress-header">
              <span>AI正在生成方案...</span>
              <span>{{ generationStep }}</span>
            </div>
            <el-progress :percentage="generationProgress" :stroke-width="8" />
            <div class="progress-steps">
              <div
                v-for="step in generationSteps"
                :key="step.key"
                class="progress-step"
                :class="{ active: step.key === currentStep, completed: step.completed }"
              >
                <el-icon>
                  <Check v-if="step.completed" />
                  <Loading v-else-if="step.key === currentStep" />
                  <component :is="step.icon" v-else />
                </el-icon>
                <span>{{ step.name }}</span>
              </div>
            </div>
          </div>

          <!-- 方案内容 -->
          <div v-if="generatedSolution && !generating" class="solution-content">
            <div class="solution-tabs">
              <el-tabs v-model="activeTab" type="border-card">
                <el-tab-pane
                  v-for="section in solutionSections"
                  :key="section.key"
                  :label="section.title"
                  :name="section.key"
                >
                  <div class="section-content">
                    <div class="section-header-info">
                      <h3>{{ section.title }}</h3>
                      <div class="section-meta">
                        <span>字数：{{ getWordCount(section.content) }}</span>
                        <span>完成度：{{ section.completeness }}%</span>
                      </div>
                    </div>
                    <div class="section-text" v-html="formatContent(section.content)"></div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!generatedSolution && !generating" class="empty-solution">
            <el-empty description="暂无生成的方案">
              <el-button
                type="primary"
                @click="generateSolution"
                :disabled="selectedRequirements.length === 0"
              >
                开始生成方案
              </el-button>
            </el-empty>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 需求选择对话框 -->
    <el-dialog v-model="showRequirementSelector" title="选择需求" width="800px">
      <div class="requirement-selector">
        <div class="selector-toolbar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索需求..."
            prefix-icon="Search"
            clearable
          />
          <el-select v-model="filterCategory" placeholder="筛选分类" clearable>
            <el-option label="业务需求" value="business" />
            <el-option label="技术需求" value="technical" />
            <el-option label="预算需求" value="budget" />
            <el-option label="时间需求" value="timeline" />
          </el-select>
        </div>

        <div class="requirement-list">
          <el-checkbox-group v-model="selectedRequirementIds">
            <div
              v-for="req in filteredRequirements"
              :key="req.id"
              class="selectable-requirement"
            >
              <el-checkbox :label="req.id">
                <div class="req-info">
                  <div class="req-title">{{ req.title }}</div>
                  <div class="req-description">{{ req.description }}</div>
                  <div class="req-tags">
                    <el-tag size="small" :type="getPriorityType(req.priority)">
                      {{ req.priority }}
                    </el-tag>
                    <el-tag size="small" type="info">
                      {{ getCategoryName(req.category) }}
                    </el-tag>
                  </div>
                </div>
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showRequirementSelector = false">取消</el-button>
        <el-button type="primary" @click="confirmRequirementSelection">
          确定选择 ({{ selectedRequirementIds.length }})
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Plus,
  Refresh,
  Magic,
  Setting,
  DocumentChecked,
  CopyDocument,
  Download,
  Edit,
  Close,
  Check,
  Loading,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const generating = ref(false)
const showRequirementSelector = ref(false)
const activeTab = ref('background')
const searchKeyword = ref('')
const filterCategory = ref('')
const selectedRequirementIds = ref([])
const generationProgress = ref(0)
const currentStep = ref('')

// 项目信息
const projectInfo = reactive({
  name: '',
  customer: '',
  industry: ''
})

// 生成配置
const generationConfig = reactive({
  type: 'comprehensive',
  detail: '标准',
  modules: ['background', 'analysis', 'solution', 'implementation', 'budget'],
  model: 'gpt-4'
})

// 选中的需求
const selectedRequirements = ref([])

// 生成的方案
const generatedSolution = ref(null)

// 模拟需求数据
const allRequirements = ref([
  {
    id: 1,
    title: '提升运营效率',
    description: '通过数字化手段提升整体运营效率',
    priority: '高',
    category: 'business'
  },
  {
    id: 2,
    title: '云端部署',
    description: '系统需要支持云端部署和扩展',
    priority: '中',
    category: 'technical'
  },
  {
    id: 3,
    title: '预算控制',
    description: '项目预算控制在100万以内',
    priority: '高',
    category: 'budget'
  }
])

// 生成步骤
const generationSteps = [
  { key: 'analyze', name: '需求分析', icon: 'Search', completed: false },
  { key: 'research', name: '方案研究', icon: 'Document', completed: false },
  { key: 'generate', name: '内容生成', icon: 'Magic', completed: false },
  { key: 'optimize', name: '方案优化', icon: 'Setting', completed: false }
]

// 计算属性
const filteredRequirements = computed(() => {
  let filtered = allRequirements.value

  if (searchKeyword.value) {
    filtered = filtered.filter(req =>
      req.title.includes(searchKeyword.value) ||
      req.description.includes(searchKeyword.value)
    )
  }

  if (filterCategory.value) {
    filtered = filtered.filter(req => req.category === filterCategory.value)
  }

  return filtered
})

const generationStep = computed(() => {
  const step = generationSteps.find(s => s.key === currentStep.value)
  return step ? step.name : '准备中...'
})

const solutionSections = computed(() => {
  if (!generatedSolution.value) return []
  
  return [
    {
      key: 'background',
      title: '项目背景',
      content: generatedSolution.value.background || '',
      completeness: 95
    },
    {
      key: 'analysis',
      title: '需求分析',
      content: generatedSolution.value.analysis || '',
      completeness: 90
    },
    {
      key: 'solution',
      title: '解决方案',
      content: generatedSolution.value.solution || '',
      completeness: 88
    },
    {
      key: 'implementation',
      title: '实施计划',
      content: generatedSolution.value.implementation || '',
      completeness: 85
    },
    {
      key: 'budget',
      title: '预算估算',
      content: generatedSolution.value.budget || '',
      completeness: 92
    }
  ]
})

// 方法
const getPriorityType = (priority) => {
  const types = { '高': 'danger', '中': 'warning', '低': 'info' }
  return types[priority] || 'info'
}

const getCategoryName = (category) => {
  const names = {
    business: '业务需求',
    technical: '技术需求',
    budget: '预算需求',
    timeline: '时间需求'
  }
  return names[category] || category
}

const loadRequirements = () => {
  ElMessage.success('需求数据已刷新')
}

const removeRequirement = (id) => {
  const index = selectedRequirements.value.findIndex(req => req.id === id)
  if (index > -1) {
    selectedRequirements.value.splice(index, 1)
    ElMessage.success('需求已移除')
  }
}

const confirmRequirementSelection = () => {
  selectedRequirements.value = allRequirements.value.filter(req =>
    selectedRequirementIds.value.includes(req.id)
  )
  showRequirementSelector.value = false
  ElMessage.success(`已选择 ${selectedRequirements.value.length} 个需求`)
}

const generateSolution = async () => {
  if (selectedRequirements.value.length === 0) {
    ElMessage.warning('请先选择需求信息')
    return
  }

  if (!projectInfo.name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }

  generating.value = true
  generationProgress.value = 0
  currentStep.value = 'analyze'

  try {
    // 模拟生成过程
    for (let i = 0; i < generationSteps.length; i++) {
      const step = generationSteps[i]
      currentStep.value = step.key
      
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      step.completed = true
      generationProgress.value = ((i + 1) / generationSteps.length) * 100
    }

    // 生成模拟方案内容
    generatedSolution.value = {
      background: generateBackgroundContent(),
      analysis: generateAnalysisContent(),
      solution: generateSolutionContent(),
      implementation: generateImplementationContent(),
      budget: generateBudgetContent()
    }

    ElMessage.success('方案生成完成')
    activeTab.value = 'background'

  } catch (error) {
    ElMessage.error('方案生成失败')
  } finally {
    generating.value = false
    // 重置步骤状态
    generationSteps.forEach(step => {
      step.completed = false
    })
  }
}

const generateBackgroundContent = () => {
  return `
    <h3>项目概述</h3>
    <p>${projectInfo.customer}作为${getCategoryName(projectInfo.industry)}领域的重要企业，面临着数字化转型的迫切需求。本项目"${projectInfo.name}"旨在通过先进的技术手段，解决企业在运营过程中遇到的关键问题。</p>
    
    <h3>现状分析</h3>
    <p>经过深入调研，我们发现企业当前存在以下主要挑战：</p>
    <ul>
      ${selectedRequirements.value.map(req => `<li>${req.description}</li>`).join('')}
    </ul>
    
    <h3>项目目标</h3>
    <p>通过本项目的实施，预期达到以下目标：</p>
    <ul>
      <li>提升运营效率30%以上</li>
      <li>降低人工成本20%</li>
      <li>提高客户满意度</li>
      <li>建立数字化管理体系</li>
    </ul>
  `
}

const generateAnalysisContent = () => {
  return `
    <h3>需求分类</h3>
    <p>根据收集到的需求信息，我们将其分为以下几个类别：</p>
    
    <h4>业务需求</h4>
    <ul>
      ${selectedRequirements.value.filter(req => req.category === 'business').map(req => `<li><strong>${req.title}</strong>：${req.description}</li>`).join('')}
    </ul>
    
    <h4>技术需求</h4>
    <ul>
      ${selectedRequirements.value.filter(req => req.category === 'technical').map(req => `<li><strong>${req.title}</strong>：${req.description}</li>`).join('')}
    </ul>
    
    <h3>需求优先级</h3>
    <p>基于业务影响和实施难度，我们对需求进行了优先级排序：</p>
    <ol>
      ${selectedRequirements.value.sort((a, b) => {
        const priority = { '高': 3, '中': 2, '低': 1 }
        return priority[b.priority] - priority[a.priority]
      }).map(req => `<li>${req.title} (${req.priority}优先级)</li>`).join('')}
    </ol>
  `
}

const generateSolutionContent = () => {
  return `
    <h3>整体架构</h3>
    <p>基于需求分析，我们设计了一套完整的解决方案架构，包含以下核心模块：</p>
    
    <h4>核心功能模块</h4>
    <ul>
      <li><strong>用户管理模块</strong>：支持多角色权限管理</li>
      <li><strong>业务流程模块</strong>：自动化核心业务流程</li>
      <li><strong>数据分析模块</strong>：提供实时数据分析和报表</li>
      <li><strong>集成接口模块</strong>：与现有系统无缝集成</li>
    </ul>
    
    <h4>技术选型</h4>
    <ul>
      <li><strong>前端技术</strong>：Vue3 + Element Plus</li>
      <li><strong>后端技术</strong>：Python FastAPI / Java Spring Boot</li>
      <li><strong>数据库</strong>：PostgreSQL + Redis</li>
      <li><strong>部署方案</strong>：Docker + Kubernetes</li>
    </ul>
    
    <h4>安全保障</h4>
    <ul>
      <li>数据加密传输和存储</li>
      <li>多层次权限控制</li>
      <li>完整的审计日志</li>
      <li>定期安全评估</li>
    </ul>
  `
}

const generateImplementationContent = () => {
  return `
    <h3>实施阶段</h3>
    <p>项目实施分为四个主要阶段：</p>
    
    <h4>第一阶段：需求确认与设计 (2周)</h4>
    <ul>
      <li>详细需求确认</li>
      <li>系统架构设计</li>
      <li>UI/UX设计</li>
      <li>技术方案评审</li>
    </ul>
    
    <h4>第二阶段：核心开发 (6周)</h4>
    <ul>
      <li>后端API开发</li>
      <li>前端界面开发</li>
      <li>数据库设计实现</li>
      <li>单元测试</li>
    </ul>
    
    <h4>第三阶段：集成测试 (2周)</h4>
    <ul>
      <li>系统集成测试</li>
      <li>性能测试</li>
      <li>安全测试</li>
      <li>用户验收测试</li>
    </ul>
    
    <h4>第四阶段：部署上线 (1周)</h4>
    <ul>
      <li>生产环境部署</li>
      <li>数据迁移</li>
      <li>用户培训</li>
      <li>系统上线</li>
    </ul>
    
    <h3>风险控制</h3>
    <ul>
      <li>定期进度评审</li>
      <li>关键节点验收</li>
      <li>风险预警机制</li>
      <li>应急预案制定</li>
    </ul>
  `
}

const generateBudgetContent = () => {
  return `
    <h3>费用构成</h3>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <tr style="background: #f5f5f5;">
        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">项目</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">费用(万元)</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">说明</th>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 12px;">需求分析与设计</td>
        <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">15</td>
        <td style="border: 1px solid #ddd; padding: 12px;">包含需求调研、系统设计等</td>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 12px;">系统开发</td>
        <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">45</td>
        <td style="border: 1px solid #ddd; padding: 12px;">前后端开发、数据库设计</td>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 12px;">测试与部署</td>
        <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">20</td>
        <td style="border: 1px solid #ddd; padding: 12px;">系统测试、部署上线</td>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 12px;">培训与维护</td>
        <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">10</td>
        <td style="border: 1px solid #ddd; padding: 12px;">用户培训、一年维护</td>
      </tr>
      <tr style="background: #f5f5f5; font-weight: bold;">
        <td style="border: 1px solid #ddd; padding: 12px;">总计</td>
        <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">90</td>
        <td style="border: 1px solid #ddd; padding: 12px;">-</td>
      </tr>
    </table>
    
    <h3>付款方式</h3>
    <ul>
      <li><strong>首付款</strong>：合同签订后支付30% (27万元)</li>
      <li><strong>进度款</strong>：开发完成后支付40% (36万元)</li>
      <li><strong>验收款</strong>：系统验收后支付25% (22.5万元)</li>
      <li><strong>质保金</strong>：质保期满后支付5% (4.5万元)</li>
    </ul>
    
    <h3>投资回报</h3>
    <p>预计项目实施后，每年可为企业节省成本约30万元，投资回报期约3年。</p>
  `
}

const getWordCount = (content) => {
  return content.replace(/<[^>]*>/g, '').length
}

const formatContent = (content) => {
  return content
}

const copySolution = async () => {
  try {
    const fullContent = solutionSections.value.map(section => 
      `${section.title}\n\n${section.content.replace(/<[^>]*>/g, '')}`
    ).join('\n\n---\n\n')
    
    await navigator.clipboard.writeText(fullContent)
    ElMessage.success('方案内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const exportSolution = () => {
  ElMessage.info('导出功能开发中')
}

const editSolution = () => {
  ElMessage.info('编辑功能开发中')
}

// 组件挂载
onMounted(() => {
  // 初始化选中一些需求
  selectedRequirements.value = allRequirements.value.slice(0, 2)
  selectedRequirementIds.value = selectedRequirements.value.map(req => req.id)
})
</script>

<style scoped>
.solution-generation {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.generation-container {
  flex: 1;
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 20px;
  overflow: hidden;
}

.requirements-section,
.solution-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.requirements-card,
.generation-config,
.solution-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.requirement-overview {
  margin-bottom: 20px;
}

.overview-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.overview-label {
  width: 80px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.requirements-list {
  flex: 1;
  overflow-y: auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.requirement-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  transition: all 0.2s;
}

.requirement-item:hover {
  border-color: var(--el-color-primary);
  background: var(--el-fill-color-extra-light);
}

.req-content {
  flex: 1;
}

.req-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.req-description {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.req-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.req-category {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.empty-requirements {
  text-align: center;
  padding: 40px 20px;
}

.generation-progress {
  padding: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.progress-step.active {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.progress-step.completed {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.solution-content {
  flex: 1;
  overflow: hidden;
}

.solution-tabs {
  height: 100%;
}

.solution-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.section-content {
  padding: 20px;
}

.section-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.section-text {
  line-height: 1.6;
}

.section-text :deep(h3) {
  color: var(--el-text-color-primary);
  margin: 20px 0 12px 0;
}

.section-text :deep(h4) {
  color: var(--el-text-color-regular);
  margin: 16px 0 8px 0;
}

.section-text :deep(ul), 
.section-text :deep(ol) {
  margin: 12px 0;
  padding-left: 20px;
}

.section-text :deep(li) {
  margin: 6px 0;
}

.empty-solution {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.requirement-selector {
  max-height: 500px;
  overflow-y: auto;
}

.selector-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.requirement-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selectable-requirement {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s;
}

.selectable-requirement:hover {
  border-color: var(--el-color-primary);
  background: var(--el-fill-color-extra-light);
}

.req-info {
  margin-left: 24px;
}

.req-tags {
  display: flex;
  gap: 6px;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .generation-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .requirements-section {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .progress-steps {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .selector-toolbar {
    flex-direction: column;
  }
}
</style>
