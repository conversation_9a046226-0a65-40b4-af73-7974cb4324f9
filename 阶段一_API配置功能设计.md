# 阶段一：API配置功能设计

## 1. API密钥管理功能设计

### 1.1 功能概述
为系统管理员提供安全、便捷的AI模型API密钥管理界面，支持多平台API的统一管理。

### 1.2 支持的AI平台
- **OpenAI**: GPT-4, GPT-4-turbo, GPT-3.5-turbo
- **DeepSeek**: DeepSeek-Chat, DeepSeek-Coder
- **Claude**: Claude-3-Opus, Claude-3-Sonnet, Claude-3-Haiku
- **其他**: 支持自定义API端点

### 1.3 界面设计

#### 1.3.1 API密钥列表页面
```
┌─────────────────────────────────────────────────────────────┐
│ AI模型API配置管理                                              │
├─────────────────────────────────────────────────────────────┤
│ [+ 添加新API]  [批量导入]  [导出配置]                          │
├─────────────────────────────────────────────────────────────┤
│ 平台     │ 模型名称        │ 状态    │ 最后测试  │ 操作        │
├─────────────────────────────────────────────────────────────┤
│ OpenAI   │ GPT-4          │ 🟢 正常  │ 2分钟前   │ [编辑][测试][删除] │
│ DeepSeek │ DeepSeek-Chat  │ 🟡 警告  │ 1小时前   │ [编辑][测试][删除] │
│ Claude   │ Claude-3-Sonnet│ 🔴 异常  │ 3小时前   │ [编辑][测试][删除] │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3.2 API密钥添加/编辑表单
```
┌─────────────────────────────────────────────────────────────┐
│ 添加/编辑 API配置                                              │
├─────────────────────────────────────────────────────────────┤
│ 基本信息                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 平台类型: [OpenAI ▼]                                     │ │
│ │ 模型名称: [GPT-4 ▼]                                      │ │
│ │ 显示名称: [生产环境-GPT4]                                  │ │
│ │ API端点:  [https://api.openai.com/v1]                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 认证信息                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ API密钥:  [sk-***************************] [显示/隐藏]    │ │
│ │ 组织ID:   [org-***************] (可选)                   │ │
│ │ 项目ID:   [proj-**************] (可选)                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 安全设置                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ☑ 启用IP白名单限制                                        │ │
│ │ 允许IP: [***********/24, 10.0.0.0/8]                   │ │
│ │ ☑ 启用时间窗口限制                                        │ │
│ │ 使用时间: [09:00] - [18:00]                             │ │
│ │ 密钥轮换: [30天 ▼] 自动提醒                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [测试连接] [保存] [取消]                                       │
└─────────────────────────────────────────────────────────────┘
```

### 1.4 安全特性

#### 1.4.1 密钥加密存储
- 使用AES-256加密存储API密钥
- 密钥分片存储，避免单点泄露
- 支持硬件安全模块(HSM)集成

#### 1.4.2 访问控制
- 基于角色的访问控制(RBAC)
- 操作审计日志记录
- 密钥访问频率监控

#### 1.4.3 密钥轮换
- 自动密钥过期提醒
- 支持密钥版本管理
- 平滑密钥切换机制

## 2. 模型参数配置界面设计

### 2.1 参数配置面板
```
┌─────────────────────────────────────────────────────────────┐
│ 模型参数配置 - GPT-4                                           │
├─────────────────────────────────────────────────────────────┤
│ 基础参数                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Temperature:    [0.7    ] ←─────────→ [0.0 - 2.0]       │ │
│ │                 创造性 ←─────────→ 确定性                  │ │
│ │                                                         │ │
│ │ Max Tokens:     [2048   ] ←─────────→ [1 - 4096]        │ │
│ │                 响应长度限制                              │ │
│ │                                                         │ │
│ │ Top P:          [1.0    ] ←─────────→ [0.0 - 1.0]       │ │
│ │                 词汇多样性控制                            │ │
│ │                                                         │ │
│ │ Frequency Penalty: [0.0] ←─────────→ [-2.0 - 2.0]       │ │
│ │                 重复词汇惩罚                              │ │
│ │                                                         │ │
│ │ Presence Penalty:  [0.0] ←─────────→ [-2.0 - 2.0]       │ │
│ │                 新话题鼓励                                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 高级参数                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Stop Sequences: ["\n\n", "###"]                        │ │
│ │ Seed:          [随机 ▼] 或 [12345]                       │ │
│ │ Response Format: [text ▼] (text/json_object)           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 预设模板                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [创意写作] [技术文档] [客服对话] [代码生成] [自定义]          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [实时测试] [保存配置] [重置默认] [导出配置]                      │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 参数预设管理
- **创意写作**: Temperature=1.2, Top P=0.9
- **技术文档**: Temperature=0.3, Top P=0.8
- **客服对话**: Temperature=0.7, Max Tokens=1024
- **代码生成**: Temperature=0.1, Stop Sequences=["\n\n"]

## 3. API调用限制设置功能

### 3.1 限制类型

#### 3.1.1 频率限制
```
┌─────────────────────────────────────────────────────────────┐
│ API调用频率限制                                               │
├─────────────────────────────────────────────────────────────┤
│ 全局限制                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 每分钟调用次数: [100] 次                                  │ │
│ │ 每小时调用次数: [1000] 次                                 │ │
│ │ 每日调用次数:   [10000] 次                                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 用户级别限制                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 普通用户: [10/分钟] [100/小时] [500/天]                   │ │
│ │ VIP用户:  [50/分钟] [500/小时] [2000/天]                  │ │
│ │ 管理员:   [无限制]                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 模型级别限制                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GPT-4:     [20/分钟] (高成本模型)                         │ │
│ │ GPT-3.5:   [100/分钟] (标准模型)                          │ │
│ │ DeepSeek:  [200/分钟] (经济模型)                          │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 3.1.2 成本控制
```
┌─────────────────────────────────────────────────────────────┐
│ API成本控制                                                   │
├─────────────────────────────────────────────────────────────┤
│ 预算设置                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 月度预算:     [¥5000] 元                                  │ │
│ │ 当前使用:     [¥1200] 元 (24%)                            │ │
│ │ 预警阈值:     [80%] 时发送通知                             │ │
│ │ 停用阈值:     [95%] 时自动停用API                          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 成本分配                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 部门A: [¥2000] (40%)  [已用: ¥480]                       │ │
│ │ 部门B: [¥1500] (30%)  [已用: ¥360]                       │ │
│ │ 部门C: [¥1500] (30%)  [已用: ¥360]                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 成本优化建议                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 💡 建议使用DeepSeek替代GPT-4处理简单任务，可节省60%成本     │ │
│ │ 💡 当前平均Token使用率偏高，建议优化Prompt减少无效输出      │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 4. 多模型切换和负载均衡

### 4.1 智能路由策略
```
┌─────────────────────────────────────────────────────────────┐
│ 多模型智能路由配置                                             │
├─────────────────────────────────────────────────────────────┤
│ 路由策略                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ○ 成本优先: 优先使用成本最低的可用模型                      │ │
│ │ ● 性能优先: 优先使用性能最佳的可用模型                      │ │
│ │ ○ 负载均衡: 平均分配请求到所有可用模型                      │ │
│ │ ○ 自定义规则: 基于业务场景智能选择                          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 模型权重配置                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GPT-4:     [30%] ████████████░░░░░░░░░░░░░░░░░░░░         │ │
│ │ Claude-3:  [40%] ████████████████░░░░░░░░░░░░░░░░         │ │
│ │ DeepSeek:  [30%] ████████████░░░░░░░░░░░░░░░░░░░░         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 故障转移设置                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ☑ 启用自动故障转移                                        │ │
│ │ 超时时间: [30] 秒                                         │ │
│ │ 重试次数: [3] 次                                          │ │
│ │ 故障转移顺序: GPT-4 → Claude-3 → DeepSeek                │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 业务场景路由规则
```
┌─────────────────────────────────────────────────────────────┐
│ 业务场景智能路由                                               │
├─────────────────────────────────────────────────────────────┤
│ 场景     │ 优先模型      │ 备选模型      │ 特殊配置            │
├─────────────────────────────────────────────────────────────┤
│ 需求收集 │ Claude-3      │ GPT-4        │ Temperature=0.7    │
│ 方案生成 │ GPT-4         │ Claude-3     │ Max_tokens=4096    │
│ 报价计算 │ DeepSeek      │ GPT-3.5      │ Temperature=0.1    │
│ 合同生成 │ Claude-3      │ GPT-4        │ 严格格式检查        │
│ 客户对话 │ GPT-3.5       │ DeepSeek     │ 快速响应模式        │
└─────────────────────────────────────────────────────────────┘
```

## 5. API状态监控和日志系统

### 5.1 实时监控面板
```
┌─────────────────────────────────────────────────────────────┐
│ API状态监控面板                                               │
├─────────────────────────────────────────────────────────────┤
│ 总体状态: 🟢 正常运行  │  活跃连接: 45  │  今日调用: 2,847    │
├─────────────────────────────────────────────────────────────┤
│ 模型状态                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GPT-4      🟢 正常    响应时间: 1.2s   成功率: 99.8%      │ │
│ │ Claude-3   🟡 缓慢    响应时间: 3.5s   成功率: 98.2%      │ │
│ │ DeepSeek   🟢 正常    响应时间: 0.8s   成功率: 99.9%      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 性能指标 (最近1小时)                                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 调用量:    ████████████████████████████████████████      │ │
│ │ 响应时间:  ████████████████████████████████████████      │ │
│ │ 错误率:    ████████████████████████████████████████      │ │
│ │ 成本:      ████████████████████████████████████████      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 告警信息                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔴 Claude-3 API响应时间超过阈值 (14:23)                   │ │
│ │ 🟡 GPT-4 调用量接近限制 (14:15)                           │ │
│ │ 🟢 DeepSeek API密钥将在7天后过期 (14:00)                  │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 详细日志查看
```
┌─────────────────────────────────────────────────────────────┐
│ API调用日志                                                   │
├─────────────────────────────────────────────────────────────┤
│ 筛选: [今天▼] [所有模型▼] [所有状态▼] [搜索...]               │
├─────────────────────────────────────────────────────────────┤
│时间      │用户    │模型     │状态│响应时间│Token│成本  │操作   │
├─────────────────────────────────────────────────────────────┤
│14:25:33 │张三    │GPT-4    │✓  │1.2s   │1024│¥0.15│[详情] │
│14:25:28 │李四    │Claude-3 │✗  │-      │-   │-    │[详情] │
│14:25:15 │王五    │DeepSeek │✓  │0.8s   │512 │¥0.03│[详情] │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 错误分析和告警
- **自动错误分类**: 网络错误、认证错误、配额超限、模型错误
- **智能告警**: 基于错误率、响应时间、成本异常的智能告警
- **故障自愈**: 自动重试、故障转移、降级策略

## 6. 技术实现方案

### 6.1 后端架构
- **API网关**: 统一API入口，负责路由和限流
- **配置服务**: 管理API密钥和参数配置
- **监控服务**: 实时监控和日志收集
- **调度服务**: 负载均衡和故障转移

### 6.2 数据库设计
```sql
-- API配置表
CREATE TABLE api_configs (
    id BIGINT PRIMARY KEY,
    platform VARCHAR(50) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    api_key_encrypted TEXT NOT NULL,
    endpoint_url VARCHAR(500),
    parameters JSON,
    rate_limits JSON,
    status ENUM('active', 'inactive', 'error'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- API调用日志表
CREATE TABLE api_call_logs (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    api_config_id BIGINT,
    request_data JSON,
    response_data JSON,
    status_code INT,
    response_time_ms INT,
    token_count INT,
    cost_amount DECIMAL(10,4),
    error_message TEXT,
    created_at TIMESTAMP
);
```

### 6.3 安全措施
- **密钥加密**: AES-256加密存储
- **访问控制**: JWT认证 + RBAC权限
- **审计日志**: 完整的操作审计记录
- **网络安全**: HTTPS + IP白名单

这完成了阶段一的API配置功能设计。请确认是否可以进入阶段二：AI约束机制设计？
